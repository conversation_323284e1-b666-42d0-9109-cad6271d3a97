#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试服务下架功能（简化版）
"""

import requests
import json

BASE_URL = "http://localhost:5000/api"

def get_merchant_token():
    """获取商家登录Token"""
    login_data = {
        "merchant_account": "petshop001",
        "merchant_password": "123456"
    }
    
    response = requests.post(f"{BASE_URL}/merchant/auth/login", json=login_data)
    if response.status_code == 200:
        data = response.json()
        return data['data']['token']
    else:
        print(f"商家登录失败: {response.text}")
        return None

def get_merchant_services(token):
    """获取商家服务列表"""
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/merchant/services", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        return data['data']['services']
    else:
        print(f"获取服务列表失败: {response.text}")
        return []

def test_offline_service(token, service_id, description):
    """测试下架服务"""
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.put(f"{BASE_URL}/merchant/services/{service_id}/offline", headers=headers)
        
        result = response.json()
        
        if response.status_code == 200 and result.get('code') == 200:
            print(f"✅ {description}")
            print(f"   Service ID: {service_id}")
            print(f"   Result: {result.get('message')}")
            print(f"   Service Name: {result['data'].get('service_name')}")
        else:
            print(f"❌ {description}")
            print(f"   Service ID: {service_id}")
            print(f"   HTTP Status: {response.status_code}")
            print(f"   Error: {result.get('message')}")
        print()
        
        return result
        
    except Exception as e:
        print(f"❌ {description}")
        print(f"   Error: {e}")
        print()
        return None

def test_user_get_services(description):
    """测试用户端获取服务（应该只看到上架的）"""
    try:
        response = requests.get(f"{BASE_URL}/services")
        
        if response.status_code == 200:
            data = response.json()
            services = data['data']['services']
            print(f"✅ {description}")
            print(f"   Found {len(services)} services (only active should be visible)")
            for service in services:
                status = "上架" if service.get('is_active', True) else "下架"
                print(f"   - {service['service_name']} ({status})")
        else:
            print(f"❌ {description}")
            print(f"   HTTP Status: {response.status_code}")
        print()
        
    except Exception as e:
        print(f"❌ {description}")
        print(f"   Error: {e}")
        print()

def test_error_scenarios(token):
    """测试错误场景"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("📋 测试错误场景:")
    
    # 1. 无效服务ID格式
    response = requests.put(f"{BASE_URL}/merchant/services/INVALID_ID/offline", headers=headers)
    result = response.json()
    if result.get('code') == 30016:
        print("✅ 无效服务ID格式 - 正确返回错误码30016")
    else:
        print(f"❌ 无效服务ID格式 - 期望30016，实际{result.get('code')}")
    
    # 2. 不存在的服务ID
    response = requests.put(f"{BASE_URL}/merchant/services/S9999999999/offline", headers=headers)
    result = response.json()
    if result.get('code') == 30001:
        print("✅ 不存在的服务ID - 正确返回错误码30001")
    else:
        print(f"❌ 不存在的服务ID - 期望30001，实际{result.get('code')}")
    
    # 3. 重复下架已下架的服务
    services = get_merchant_services(token)
    offline_service = None
    for service in services:
        if not service.get('is_active', True):
            offline_service = service
            break
    
    if offline_service:
        response = requests.put(f"{BASE_URL}/merchant/services/{offline_service['service_id']}/offline", headers=headers)
        result = response.json()
        if result.get('code') == 32005:
            print("✅ 重复下架已下架服务 - 正确返回错误码32005")
        else:
            print(f"❌ 重复下架已下架服务 - 期望32005，实际{result.get('code')}")
    else:
        print("⚠️  没有找到已下架的服务，跳过重复下架测试")
    
    print()

def main():
    print("🧪 测试服务下架功能（简化版）...")
    print("=" * 60)
    
    # 获取商家Token
    print("🔑 获取商家登录Token...")
    token = get_merchant_token()
    if not token:
        print("❌ 无法获取商家Token，测试终止")
        return
    print(f"✅ 成功获取Token")
    print()
    
    # 获取商家服务列表
    print("📋 获取商家服务列表...")
    services = get_merchant_services(token)
    if not services:
        print("❌ 没有找到服务，测试终止")
        return
    
    print(f"✅ 找到 {len(services)} 个服务:")
    for service in services:
        status = "上架" if service.get('is_active', True) else "下架"
        print(f"   - {service['service_name']} ({service['service_id']}) - {status}")
    print()
    
    # 测试下架功能
    active_service = None
    for service in services:
        if service.get('is_active', True):
            active_service = service
            break
    
    if active_service:
        print("📋 测试下架功能:")
        test_offline_service(token, active_service['service_id'], 
                           f"下架服务: {active_service['service_name']}")
    else:
        print("⚠️  没有找到上架的服务，跳过下架测试")
        print()
    
    # 测试用户端只能看到上架服务
    print("📋 测试用户端服务可见性:")
    test_user_get_services("用户端获取服务（应该只显示上架的）")
    
    # 测试错误场景
    test_error_scenarios(token)
    
    # 再次查看服务列表状态
    print("📋 下架后的服务列表:")
    services_after = get_merchant_services(token)
    for service in services_after:
        status = "上架" if service.get('is_active', True) else "下架"
        print(f"   - {service['service_name']} ({service['service_id']}) - {status}")
    print()
    
    print("🎉 测试完成！")

if __name__ == "__main__":
    main()
