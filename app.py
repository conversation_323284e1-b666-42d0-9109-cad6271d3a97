#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
宠物服务平台 Flask API 应用
支持用户端和商家端的完整功能
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
from datetime import datetime, timedelta
import jwt
import hashlib
import random
import string
import json
import os
from functools import wraps

# 创建 Flask 应用
app = Flask(__name__)
app.config['SECRET_KEY'] = 'pet-service-platform-secret-key-2024'
CORS(app)  # 允许跨域请求

# 模拟数据存储（实际项目中应使用数据库）
users_db = {}
pets_db = {}
merchants_db = {}
services_db = {}
orders_db = {}
appointments_db = {}
evaluations_db = {}
sms_codes = {}

# JWT 配置
JWT_SECRET = app.config['SECRET_KEY']
JWT_ALGORITHM = 'HS256'

# 工具函数
def generate_id(prefix, length=8):
    """生成唯一ID"""
    suffix = ''.join(random.choices(string.digits, k=length-1))
    return f"{prefix}{suffix}"

def generate_sms_code():
    """生成6位短信验证码"""
    return ''.join(random.choices(string.digits, k=6))

def hash_password(password):
    """密码哈希"""
    return hashlib.sha256(password.encode()).hexdigest()

def create_token(user_id, user_type='user'):
    """创建JWT Token"""
    payload = {
        'user_id': user_id,
        'user_type': user_type,
        'exp': datetime.utcnow() + timedelta(days=7)
    }
    return jwt.encode(payload, JWT_SECRET, algorithm=JWT_ALGORITHM)

def verify_token(token):
    """验证JWT Token"""
    try:
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None

# 装饰器
def token_required(f):
    """Token验证装饰器"""
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        auth_header = request.headers.get('Authorization')
        
        if auth_header:
            try:
                token = auth_header.split(' ')[1]
            except IndexError:
                return jsonify({
                    'code': 401,
                    'data': None,
                    'message': 'Token格式错误'
                }), 401

        if not token:
            return jsonify({
                'code': 401,
                'data': None,
                'message': '缺少认证Token'
            }), 401

        payload = verify_token(token)
        if not payload:
            return jsonify({
                'code': 401,
                'data': None,
                'message': 'Token无效或已过期'
            }), 401
        
        request.current_user = payload
        return f(*args, **kwargs)
    
    return decorated

def success_response(data=None, message="操作成功"):
    """成功响应格式"""
    return jsonify({
        'code': 200,
        'data': data,
        'message': message
    })

def error_response(code=400, message="请求失败", data=None):
    """错误响应格式"""
    # 如果是自定义业务错误码（4位数或5位数），HTTP状态码使用400
    # 如果是标准HTTP错误码（3位数），使用错误码本身作为HTTP状态码
    if code >= 1000:  # 业务错误码（4位数或5位数）
        http_status = 400
    else:  # 标准HTTP错误码（3位数）
        http_status = code

    return jsonify({
        'code': code,
        'data': data,
        'message': message
    }), http_status

# ==================== 用户端 API ====================

# 用户认证模块
@app.route('/api/auth/send-code', methods=['POST'])
def send_sms_code():
    """发送短信验证码"""
    data = request.get_json()
    user_phone = data.get('user_phone')  # 使用数据库表字段名

    if not user_phone or len(user_phone) != 11:
        return error_response(10001, '手机号格式错误')

    # 生成验证码
    code = generate_sms_code()
    expire_time = datetime.now() + timedelta(minutes=1)

    # 存储验证码（实际项目中应发送真实短信）
    sms_codes[user_phone] = {
        'code': code,
        'expire_time': expire_time,
        'type': 'login'
    }

    print(f"短信验证码: {user_phone} -> {code}")  # 调试用，实际项目中删除

    return success_response({
        'expire_time': 300
    }, '验证码发送成功')

@app.route('/api/auth/register', methods=['POST'])
def user_register():
    """用户注册"""
    data = request.get_json()
    user_phone = data.get('user_phone')  # 使用数据库表字段名
    user_verification_code = data.get('user_verification_code')  # 使用数据库表字段名

    if not user_phone or not user_verification_code:
        return error_response(400, '手机号和验证码不能为空')

    # 验证短信验证码
    if user_phone not in sms_codes:
        return error_response(10002, '验证码不存在或已过期')

    sms_data = sms_codes[user_phone]
    if sms_data['code'] != user_verification_code:
        return error_response(10002, '验证码错误')

    if datetime.now() > sms_data['expire_time']:
        return error_response(10002, '验证码已过期')

    # 检查用户是否已存在
    for user in users_db.values():
        if user['user_phone'] == user_phone:
            return error_response(10004, '用户已存在')

    # 创建新用户（严格按照数据库表结构）
    user_id = generate_id('U')
    user_info = {
        'user_id': user_id,                    # varchar(8)
        'user_phone': user_phone,              # char(11)
        'user_verification_code': None,        # char(6)
        'user_name': user_phone,               # varchar(20) 默认用户名为手机号
        'user_sex': None                       # enum 0-男，1-女
    }

    users_db[user_id] = user_info

    # 删除已使用的验证码
    del sms_codes[user_phone]

    # 生成Token
    token = create_token(user_id, 'user')

    return success_response({
        'user_id': user_id,
        'token': token,
        'user_info': user_info
    }, '注册成功')

@app.route('/api/auth/login', methods=['POST'])
def user_login():
    """用户登录"""
    data = request.get_json()
    user_phone = data.get('user_phone')  # 使用数据库表字段名
    user_verification_code = data.get('user_verification_code')  # 使用数据库表字段名

    if not user_phone or not user_verification_code:
        return error_response(400, '手机号和验证码不能为空')

    # 验证短信验证码
    if user_phone not in sms_codes:
        return error_response(10002, '验证码不存在或已过期')

    sms_data = sms_codes[user_phone]
    if sms_data['code'] != user_verification_code:
        return error_response(10002, '验证码错误')

    if datetime.now() > sms_data['expire_time']:
        return error_response(10002, '验证码已过期')

    # 查找用户
    user_info = None
    for user in users_db.values():
        if user['user_phone'] == user_phone:
            user_info = user
            break

    if not user_info:
        return error_response(10003, '用户不存在，请先注册')

    # 删除已使用的验证码
    del sms_codes[user_phone]

    # 生成Token
    token = create_token(user_info['user_id'], 'user')

    return success_response({
        'user_id': user_info['user_id'],
        'token': token,
        'user_info': user_info
    }, '登录成功')

@app.route('/api/user/profile', methods=['PUT'])
@token_required
def update_user_profile():
    """更新用户信息"""
    if request.current_user['user_type'] != 'user':
        return error_response(403, '权限不足')
    
    user_id = request.current_user['user_id']
    data = request.get_json()
    
    if user_id not in users_db:
        return error_response(10003, '用户不存在')
    
    user_info = users_db[user_id]
    
    # 更新用户信息（严格按照数据库表结构）
    if 'user_name' in data:
        if len(data['user_name']) < 2 or len(data['user_name']) > 20:
            return error_response(400, '用户名长度应在2-20个字符之间')
        user_info['user_name'] = data['user_name']

    if 'user_sex' in data:
        if data['user_sex'] not in [0, 1]:  # enum 0-男，1-女
            return error_response(400, '性别参数错误')
        user_info['user_sex'] = data['user_sex']
    
    return success_response(user_info, '更新成功')

# 宠物管理模块
@app.route('/api/pets', methods=['POST'])
@token_required
def add_pet():
    """添加宠物"""
    if request.current_user['user_type'] != 'user':
        return error_response(403, '权限不足')

    user_id = request.current_user['user_id']
    data = request.get_json()

    required_fields = ['pet_kind', 'pet_age', 'pet_weight']
    for field in required_fields:
        if field not in data:
            return error_response(400, f'缺少必填字段: {field}')

    # 创建宠物信息（严格按照数据库表结构）
    pet_id = generate_id('P')
    pet_info = {
        'pet_id': pet_id,
        'user_id': user_id,
        'pet_kind': data['pet_kind'],  # varchar(5)
        'pet_age': data['pet_age'],    # date
        'pet_weight': data['pet_weight']  # varchar(5)
    }

    pets_db[pet_id] = pet_info

    return success_response(pet_info, '添加成功')

@app.route('/api/pets', methods=['GET'])
@token_required
def get_pets():
    """获取宠物列表"""
    if request.current_user['user_type'] != 'user':
        return error_response(403, '权限不足')

    user_id = request.current_user['user_id']

    # 获取用户的宠物列表（只返回数据库表字段）
    user_pets = []
    for pet in pets_db.values():
        if pet['user_id'] == user_id:
            # 只返回数据库表中定义的字段
            pet_data = {
                'pet_id': pet['pet_id'],
                'user_id': pet['user_id'],
                'pet_kind': pet['pet_kind'],
                'pet_age': pet['pet_age'],
                'pet_weight': pet['pet_weight']
            }
            user_pets.append(pet_data)

    return success_response(user_pets, '获取成功')

@app.route('/api/pets/<pet_id>', methods=['DELETE'])
@token_required
def delete_pet(pet_id):
    """删除宠物"""
    if request.current_user['user_type'] != 'user':
        return error_response(403, '权限不足')

    user_id = request.current_user['user_id']

    if pet_id not in pets_db:
        return error_response(20001, '宠物不存在')

    pet_info = pets_db[pet_id]
    if pet_info['user_id'] != user_id:
        return error_response(403, '无权删除此宠物')

    # 直接删除
    del pets_db[pet_id]

    return success_response(None, '删除成功')

# 服务模块
@app.route('/api/services', methods=['GET'])
def get_services():
    """获取服务列表"""
    try:
        # 参数验证和错误处理
        page_str = request.args.get('page', '1')
        limit_str = request.args.get('limit', '10')
        service_kind = request.args.get('service_kind')
        keyword = request.args.get('keyword', '')

        # 验证页码参数
        try:
            page = int(page_str)
            if page < 1:
                return error_response(30002, '页码必须大于0')
            if page > 10000:  # 设置合理的上限
                return error_response(30003, '页码超出允许范围')
        except ValueError:
            return error_response(30004, '页码格式错误，必须为正整数')

        # 验证每页数量参数
        try:
            limit = int(limit_str)
            if limit < 1:
                return error_response(30005, '每页数量必须大于0')
            if limit > 100:  # 设置合理的上限
                return error_response(30006, '每页数量不能超过100')
        except ValueError:
            return error_response(30007, '每页数量格式错误，必须为正整数')

        # 验证服务类型参数
        if service_kind is not None:
            try:
                service_kind_int = int(service_kind)
                if service_kind_int not in [0, 1, 2, 3]:  # 0-美容，1-寄养，2-清洁，3-训练
                    return error_response(30008, '服务类型无效，必须为0-3之间的整数')
                service_kind = service_kind_int
            except ValueError:
                return error_response(30009, '服务类型格式错误，必须为整数')

        # 验证关键词参数
        if keyword:
            if len(keyword) > 50:
                return error_response(30010, '搜索关键词长度不能超过50个字符')
            # 检查是否包含特殊字符（简单的安全检查）
            import re
            if not re.match(r'^[\u4e00-\u9fa5a-zA-Z0-9\s]*$', keyword):
                return error_response(30011, '搜索关键词包含非法字符')

        # 检查服务数据是否可用
        if not isinstance(services_db, dict):
            return error_response(30012, '服务数据不可用')

        # 过滤服务
        filtered_services = []
        for service in services_db.values():
            try:
                # 只显示上架的服务
                if not service.get('is_active', True):
                    continue

                # 按类型过滤
                if service_kind is not None and service.get('service_kind') != service_kind:
                    continue

                # 按关键词过滤
                if keyword and keyword not in service.get('service_name', ''):
                    continue

                # 验证服务数据完整性
                required_fields = ['service_id', 'service_name', 'service_kind', 'service_price']
                if not all(field in service for field in required_fields):
                    continue  # 跳过数据不完整的服务

                filtered_services.append(service)
            except Exception as e:
                # 记录错误但继续处理其他服务
                print(f"处理服务数据时出错: {e}")
                continue

        # 计算分页
        total = len(filtered_services)

        # 检查页码是否超出范围
        max_page = (total + limit - 1) // limit if total > 0 else 1
        if page > max_page and total > 0:
            return error_response(30013, f'页码超出范围，最大页码为{max_page}')

        start = (page - 1) * limit
        end = start + limit
        services = filtered_services[start:end]

        # 返回成功结果
        return success_response({
            'total': total,
            'page': page,
            'limit': limit,
            'max_page': max_page,
            'services': services
        }, '获取成功')

    except Exception as e:
        # 捕获所有未预期的错误
        print(f"获取服务列表时发生未知错误: {e}")
        return error_response(30099, '服务暂时不可用，请稍后重试')

@app.route('/api/services/<service_id>', methods=['GET'])
def get_service_detail(service_id):
    """获取服务详情"""
    try:
        # 验证服务ID格式
        if not service_id:
            return error_response(30014, '服务ID不能为空')

        if len(service_id) > 20:
            return error_response(30015, '服务ID长度无效')

        # 验证服务ID格式（应该以S开头，后跟数字）
        import re
        if not re.match(r'^S\d{10}$', service_id):
            return error_response(30016, '服务ID格式无效，应为S+10位数字')

        # 检查服务数据是否可用
        if not isinstance(services_db, dict):
            return error_response(30012, '服务数据不可用')

        # 检查服务是否存在
        if service_id not in services_db:
            return error_response(30001, '服务不存在')

        service = services_db[service_id]

        # 验证服务数据完整性
        if not isinstance(service, dict):
            return error_response(30017, '服务数据格式错误')

        required_fields = ['service_id', 'service_name', 'service_kind', 'service_price']
        missing_fields = [field for field in required_fields if field not in service]
        if missing_fields:
            return error_response(30018, f'服务数据不完整，缺少字段: {", ".join(missing_fields)}')

        # 验证服务数据有效性
        if service.get('service_price', 0) < 0:
            return error_response(30019, '服务价格数据异常')

        if service.get('service_kind') not in [0, 1, 2, 3]:
            return error_response(30020, '服务类型数据异常')

        return success_response(service, '获取成功')

    except Exception as e:
        # 捕获所有未预期的错误
        print(f"获取服务详情时发生未知错误: {e}")
        return error_response(30099, '服务暂时不可用，请稍后重试')

# 订单模块
@app.route('/api/orders', methods=['POST'])
@token_required
def create_order():
    """创建订单"""
    if request.current_user['user_type'] != 'user':
        return error_response(403, '权限不足')

    user_id = request.current_user['user_id']
    data = request.get_json()

    service_id = data.get('service_id')
    pet_id = data.get('pet_id')

    if not service_id or not pet_id:
        return error_response(400, '服务ID和宠物ID不能为空')

    # 验证服务存在
    if service_id not in services_db:
        return error_response(30001, '服务不存在')

    service = services_db[service_id]
    # 移除is_active检查，因为数据库表中没有此字段

    # 验证宠物存在且属于当前用户
    if pet_id not in pets_db:
        return error_response(20001, '宠物不存在')

    pet = pets_db[pet_id]
    if pet['user_id'] != user_id:
        return error_response(403, '无权使用此宠物')

    # 创建订单（严格按照数据库表结构，添加merchant_id字段）
    order_id = generate_id('O', 11)
    order_info = {
        'order_id': order_id,                  # varchar(11)
        'user_id': user_id,                    # varchar(8)
        'merchant_id': service.get('merchant_id', ''),  # varchar(8) 关联商家
        'service_id': service_id,              # varchar(11)
        'order_status': 4,                     # enum 0-已取消，1-已支付，2-已预约，3-已完成，4-未支付
        'order_price': service['service_price'], # Decimal(10,2)
        'pet_id': pet_id                       # varchar(8)
    }

    orders_db[order_id] = order_info

    # 模拟支付URL
    payment_url = f"https://pay.alipay.com/order/{order_id}"

    return success_response({
        **order_info,
        'payment_url': payment_url
    }, '订单创建成功')

@app.route('/api/orders/<order_id>/payment-callback', methods=['POST'])
def payment_callback(order_id):
    """支付回调"""
    data = request.get_json()

    if order_id not in orders_db:
        return error_response(40001, '订单不存在')

    order = orders_db[order_id]

    # 模拟支付成功
    payment_status = data.get('payment_status', 1)
    if payment_status == 1:
        order['order_status'] = 1  # 已支付
        order['updated_at'] = datetime.now().isoformat()

        return success_response({
            'order_id': order_id,
            'order_status': 1
        }, '支付成功')
    else:
        return error_response(40003, '支付失败')

@app.route('/api/orders', methods=['GET'])
@token_required
def get_orders():
    """获取订单列表"""
    if request.current_user['user_type'] != 'user':
        return error_response(403, '权限不足')

    user_id = request.current_user['user_id']
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 10))
    order_status = request.args.get('order_status')

    # 过滤用户订单
    user_orders = []
    for order in orders_db.values():
        if order['user_id'] != user_id:
            continue

        if order_status is not None and str(order['order_status']) != str(order_status):
            continue

        # 添加关联信息
        order_detail = order.copy()
        if order['service_id'] in services_db:
            service = services_db[order['service_id']]
            order_detail['service_name'] = service['service_name']

        if order['pet_id'] in pets_db:
            pet = pets_db[order['pet_id']]
            order_detail['pet_kind'] = pet['pet_kind']

        user_orders.append(order_detail)

    # 按订单ID倒序排序（因为数据库表中没有created_at字段）
    user_orders.sort(key=lambda x: x['order_id'], reverse=True)

    # 分页
    total = len(user_orders)
    start = (page - 1) * limit
    end = start + limit
    orders = user_orders[start:end]

    return success_response({
        'total': total,
        'page': page,
        'limit': limit,
        'orders': orders
    }, '获取成功')

@app.route('/api/orders/<order_id>', methods=['GET'])
@token_required
def get_order_detail(order_id):
    """获取订单详情"""
    if request.current_user['user_type'] != 'user':
        return error_response(403, '权限不足')

    user_id = request.current_user['user_id']

    if order_id not in orders_db:
        return error_response(40001, '订单不存在')

    order = orders_db[order_id]
    if order['user_id'] != user_id:
        return error_response(403, '无权查看此订单')

    # 添加关联信息
    order_detail = order.copy()

    if order['service_id'] in services_db:
        service = services_db[order['service_id']]
        order_detail.update({
            'service_name': service['service_name'],
            'service_description': service['service_description']
        })

    if order['pet_id'] in pets_db:
        pet = pets_db[order['pet_id']]
        order_detail.update({
            'pet_kind': pet['pet_kind'],
            'pet_weight': pet['pet_weight'],
            'pet_age': pet['pet_age']
        })

    # 查找预约信息
    appointment_info = None
    for appointment in appointments_db.values():
        if appointment['order_id'] == order_id:
            appointment_info = appointment
            break

    if appointment_info:
        order_detail['appointment_info'] = appointment_info

    return success_response(order_detail, '获取成功')

@app.route('/api/orders/<order_id>/cancel', methods=['PUT'])
@token_required
def cancel_order(order_id):
    """取消订单"""
    if request.current_user['user_type'] != 'user':
        return error_response(403, '权限不足')

    user_id = request.current_user['user_id']

    if order_id not in orders_db:
        return error_response(40001, '订单不存在')

    order = orders_db[order_id]
    if order['user_id'] != user_id:
        return error_response(403, '无权取消此订单')

    if order['order_status'] in [0, 3]:  # 已取消或已完成
        return error_response(40002, '订单状态不允许取消')

    # 取消订单
    order['order_status'] = 0  # 已取消
    order['updated_at'] = datetime.now().isoformat()

    # 如果已支付，计算退款金额
    refund_amount = 0
    if order['order_status'] == 1:  # 已支付
        refund_amount = order['order_price']

    return success_response({
        'order_id': order_id,
        'order_status': 0,
        'refund_amount': refund_amount
    }, '订单取消成功')

# 预约模块
@app.route('/api/appointments', methods=['POST'])
@token_required
def create_appointment():
    """创建预约"""
    if request.current_user['user_type'] != 'user':
        return error_response(403, '权限不足')

    user_id = request.current_user['user_id']
    data = request.get_json()

    order_id = data.get('order_id')
    appointment_time = data.get('appointment_time')
    appointment_location = data.get('appointment_location')

    if not all([order_id, appointment_time, appointment_location]):
        return error_response(400, '订单ID、预约时间和地点不能为空')

    # 验证订单
    if order_id not in orders_db:
        return error_response(40001, '订单不存在')

    order = orders_db[order_id]
    if order['user_id'] != user_id:
        return error_response(403, '无权预约此订单')

    if order['order_status'] != 1:  # 必须是已支付状态
        return error_response(40002, '订单状态不允许预约')

    # 创建预约（严格按照数据库表结构，添加merchant_id字段）
    appointment_id = generate_id('A', 11)
    appointment_info = {
        'appointment_id': appointment_id,      # varchar(11)
        'order_id': order_id,                  # varchar(11)
        'appointment_time': appointment_time,  # datetime
        'appointment_location': appointment_location, # text
        'appointment_status': 0,               # enum 预约状态
        'user_id': user_id,                    # varchar(8) 注意：这是主键
        'merchant_id': order.get('merchant_id', '')  # varchar(8) 关联商家
    }

    appointments_db[appointment_id] = appointment_info

    # 更新订单状态
    order['order_status'] = 2  # 已预约
    order['updated_at'] = datetime.now().isoformat()

    return success_response(appointment_info, '预约成功')

@app.route('/api/appointments/<appointment_id>', methods=['PUT'])
@token_required
def update_appointment(appointment_id):
    """修改预约"""
    if request.current_user['user_type'] != 'user':
        return error_response(403, '权限不足')

    user_id = request.current_user['user_id']
    data = request.get_json()

    if appointment_id not in appointments_db:
        return error_response(50002, '预约不存在')

    appointment = appointments_db[appointment_id]
    if appointment['user_id'] != user_id:
        return error_response(403, '无权修改此预约')

    if appointment['appointment_status'] in [2, 3]:  # 已取消或已完成
        return error_response(40002, '预约状态不允许修改')

    # 更新预约信息
    if 'appointment_time' in data:
        appointment['appointment_time'] = data['appointment_time']

    if 'appointment_location' in data:
        appointment['appointment_location'] = data['appointment_location']

    appointment['updated_at'] = datetime.now().isoformat()

    return success_response(appointment, '预约修改成功')

@app.route('/api/appointments/<appointment_id>/cancel', methods=['PUT'])
@token_required
def cancel_appointment(appointment_id):
    """取消预约"""
    if request.current_user['user_type'] != 'user':
        return error_response(403, '权限不足')

    user_id = request.current_user['user_id']

    if appointment_id not in appointments_db:
        return error_response(50002, '预约不存在')

    appointment = appointments_db[appointment_id]
    if appointment['user_id'] != user_id:
        return error_response(403, '无权取消此预约')

    # 取消预约
    appointment['appointment_status'] = 2  # 已取消
    appointment['updated_at'] = datetime.now().isoformat()

    return success_response({
        'appointment_id': appointment_id,
        'appointment_status': 2
    }, '预约取消成功')

# 评价模块
@app.route('/api/evaluations', methods=['POST'])
@token_required
def create_evaluation():
    """提交评价"""
    if request.current_user['user_type'] != 'user':
        return error_response(403, '权限不足')

    user_id = request.current_user['user_id']
    data = request.get_json()

    order_id = data.get('order_id')
    evaluation_star = data.get('evaluation_star')
    evaluation_context = data.get('evaluation_context', '')
    evaluation_picture = data.get('evaluation_picture', [])

    if not order_id or not evaluation_star:
        return error_response(400, '订单ID和评分不能为空')

    if evaluation_star < 1 or evaluation_star > 5:
        return error_response(400, '评分必须在1-5之间')

    # 验证订单
    if order_id not in orders_db:
        return error_response(40001, '订单不存在')

    order = orders_db[order_id]
    if order['user_id'] != user_id:
        return error_response(403, '无权评价此订单')

    if order['order_status'] != 3:  # 必须是已完成状态
        return error_response(40002, '订单状态不允许评价')

    # 检查是否已评价
    for evaluation in evaluations_db.values():
        if evaluation['order_id'] == order_id:
            return error_response(400, '该订单已评价')

    # 创建评价（严格按照数据库表结构，添加merchant_id和service_id字段）
    evaluation_id = generate_id('E', 11)
    evaluation_info = {
        'evaluation_id': evaluation_id,        # varchar(11)
        'user_id': user_id,                    # varchar(8)
        'order_id': order_id,                  # varchar(11)
        'merchant_id': order.get('merchant_id', ''),  # varchar(8) 关联商家
        'service_id': order['service_id'],     # varchar(11) 关联服务
        'evaluation_star': evaluation_star,    # tinyint unsigned
        'evaluation_context': evaluation_context, # text
        'evaluation_picture': evaluation_picture  # text
    }

    evaluations_db[evaluation_id] = evaluation_info

    return success_response(evaluation_info, '评价提交成功')

@app.route('/api/services/<service_id>/evaluations', methods=['GET'])
def get_service_evaluations(service_id):
    """获取服务评价列表"""
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 10))

    if service_id not in services_db:
        return error_response(30001, '服务不存在')

    # 获取该服务的评价（通过订单关联）
    service_evaluations = []
    total_stars = 0
    for evaluation in evaluations_db.values():
        # 通过订单ID找到对应的服务
        order_id = evaluation['order_id']
        if order_id in orders_db:
            order = orders_db[order_id]
            if order['service_id'] == service_id:
                # 添加用户信息
                evaluation_detail = evaluation.copy()
                if evaluation['user_id'] in users_db:
                    user = users_db[evaluation['user_id']]
                    evaluation_detail['user_name'] = user['user_name']

                service_evaluations.append(evaluation_detail)
                total_stars += evaluation['evaluation_star']

    # 计算平均评分
    average_star = round(total_stars / len(service_evaluations), 1) if service_evaluations else 0

    # 按评价ID倒序排序（因为数据库表中没有created_at字段）
    service_evaluations.sort(key=lambda x: x['evaluation_id'], reverse=True)

    # 分页
    total = len(service_evaluations)
    start = (page - 1) * limit
    end = start + limit
    evaluations = service_evaluations[start:end]

    return success_response({
        'total': total,
        'page': page,
        'limit': limit,
        'average_star': average_star,
        'evaluations': evaluations
    }, '获取成功')

# ==================== 商家端 API ====================

# 商家认证模块
@app.route('/api/merchant/auth/register', methods=['POST'])
def merchant_register():
    """商家注册"""
    data = request.get_json()

    required_fields = ['merchant_account', 'merchant_password', 'merchant_name']
    for field in required_fields:
        if field not in data:
            return error_response(400, f'缺少必填字段: {field}')

    merchant_account = data['merchant_account']
    merchant_password = data['merchant_password']

    # 检查账号是否已存在
    for merchant in merchants_db.values():
        if merchant['merchant_account'] == merchant_account:
            return error_response(60001, '商家账号已存在')

    # 验证账号格式
    if len(merchant_account) > 50 or not merchant_account.isalnum():
        return error_response(400, '账号格式错误')

    if len(merchant_password) > 50:
        return error_response(400, '密码长度不能超过50个字符')

    # 创建商家（严格按照数据库表结构）
    merchant_id = generate_id('M')
    merchant_info = {
        'merchant_id': merchant_id,                                    # varchar(8)
        'merchant_account': merchant_account,                          # varchar(50)
        'merchant_password': hash_password(merchant_password),         # varchar(50)
        'merchant_name': data['merchant_name'],                        # varchar(200)
        'merchant_phone': data.get('merchant_phone', ''),              # char(11)
        'merchant_location': data.get('merchant_location', '')         # text
    }

    merchants_db[merchant_id] = merchant_info

    # 生成Token
    token = create_token(merchant_id, 'merchant')

    # 返回信息（不包含密码）
    merchant_response = merchant_info.copy()
    del merchant_response['merchant_password']

    return success_response({
        'merchant_id': merchant_id,
        'token': token,
        'merchant_info': merchant_response
    }, '注册成功')

@app.route('/api/merchant/auth/login', methods=['POST'])
def merchant_login():
    """商家登录"""
    data = request.get_json()

    merchant_account = data.get('merchant_account')
    merchant_password = data.get('merchant_password')

    if not merchant_account or not merchant_password:
        return error_response(400, '账号和密码不能为空')

    # 查找商家
    merchant_info = None
    for merchant in merchants_db.values():
        if merchant['merchant_account'] == merchant_account:
            merchant_info = merchant
            break

    if not merchant_info:
        return error_response(60002, '商家账号不存在')

    # 验证密码
    if merchant_info['merchant_password'] != hash_password(merchant_password):
        return error_response(60002, '密码错误')

    # 移除状态检查，因为数据库表中没有status字段

    # 生成Token
    token = create_token(merchant_info['merchant_id'], 'merchant')

    # 返回信息（不包含密码）
    merchant_response = merchant_info.copy()
    del merchant_response['merchant_password']

    return success_response({
        'merchant_id': merchant_info['merchant_id'],
        'token': token,
        'merchant_info': merchant_response
    }, '登录成功')

@app.route('/api/merchant/profile', methods=['PUT'])
@token_required
def update_merchant_profile():
    """更新商家信息"""
    if request.current_user['user_type'] != 'merchant':
        return error_response(403, '权限不足')

    merchant_id = request.current_user['user_id']
    data = request.get_json()

    if merchant_id not in merchants_db:
        return error_response(60001, '商家不存在')

    merchant_info = merchants_db[merchant_id]

    # 更新商家信息
    if 'merchant_name' in data:
        merchant_info['merchant_name'] = data['merchant_name']

    if 'merchant_phone' in data:
        merchant_info['merchant_phone'] = data['merchant_phone']

    if 'merchant_location' in data:
        merchant_info['merchant_location'] = data['merchant_location']

    if 'merchant_password' in data:
        merchant_info['merchant_password'] = hash_password(data['merchant_password'])

    merchant_info['updated_at'] = datetime.now().isoformat()

    # 返回信息（不包含密码）
    merchant_response = merchant_info.copy()
    del merchant_response['merchant_password']

    return success_response(merchant_response, '更新成功')

# 商家服务管理模块
@app.route('/api/merchant/services', methods=['POST'])
@token_required
def add_merchant_service():
    """添加服务"""
    if request.current_user['user_type'] != 'merchant':
        return error_response(403, '权限不足')

    merchant_id = request.current_user['user_id']
    data = request.get_json()

    required_fields = ['service_name', 'service_kind', 'service_price', 'service_time']
    for field in required_fields:
        if field not in data:
            return error_response(400, f'缺少必填字段: {field}')

    # 创建服务（严格按照数据库表结构，添加merchant_id字段和is_active状态）
    service_id = generate_id('S', 11)
    service_info = {
        'service_id': service_id,                                      # varchar(11)
        'merchant_id': merchant_id,                                    # varchar(8) 关联商家
        'service_name': data['service_name'],                          # varchar(100)
        'service_kind': int(data['service_kind']),                     # enum 0-美容，1-寄养，2-清洁，3-训练
        'service_description': data.get('service_description', ''),    # text
        'service_price': float(data['service_price']),                 # Decimal(10,2)
        'service_time': int(data['service_time']),                     # int 服务时长，精确到分钟
        'service_picture': data.get('service_picture', ''),            # text 图片URL
        'is_active': True                                              # boolean 服务状态，True-上架，False-下架
    }

    services_db[service_id] = service_info

    return success_response(service_info, '服务添加成功')

@app.route('/api/merchant/services', methods=['GET'])
@token_required
def get_merchant_services():
    """获取商家服务列表"""
    try:
        # 权限验证
        if request.current_user['user_type'] != 'merchant':
            return error_response(403, '权限不足')

        merchant_id = request.current_user['user_id']

        # 参数验证和错误处理
        page_str = request.args.get('page', '1')
        limit_str = request.args.get('limit', '10')
        service_kind = request.args.get('service_kind')
        keyword = request.args.get('keyword', '')

        # 验证页码参数
        try:
            page = int(page_str)
            if page < 1:
                return error_response(31002, '页码必须大于0')
            if page > 10000:
                return error_response(31003, '页码超出允许范围')
        except ValueError:
            return error_response(31004, '页码格式错误，必须为正整数')

        # 验证每页数量参数
        try:
            limit = int(limit_str)
            if limit < 1:
                return error_response(31005, '每页数量必须大于0')
            if limit > 100:
                return error_response(31006, '每页数量不能超过100')
        except ValueError:
            return error_response(31007, '每页数量格式错误，必须为正整数')

        # 验证服务类型参数
        if service_kind is not None:
            try:
                service_kind_int = int(service_kind)
                if service_kind_int not in [0, 1, 2, 3]:
                    return error_response(31008, '服务类型无效，必须为0-3之间的整数')
                service_kind = service_kind_int
            except ValueError:
                return error_response(31009, '服务类型格式错误，必须为整数')

        # 验证关键词参数
        if keyword:
            if len(keyword) > 50:
                return error_response(31010, '搜索关键词长度不能超过50个字符')
            import re
            if not re.match(r'^[\u4e00-\u9fa5a-zA-Z0-9\s]*$', keyword):
                return error_response(31011, '搜索关键词包含非法字符')

        # 检查服务数据是否可用
        if not isinstance(services_db, dict):
            return error_response(31012, '服务数据不可用')

        # 过滤商家服务（现在service表包含merchant_id字段）
        merchant_services = []
        for service in services_db.values():
            try:
                # 只显示当前商家的服务
                if service.get('merchant_id') != merchant_id:
                    continue

                # 按类型过滤
                if service_kind is not None and service.get('service_kind') != service_kind:
                    continue

                # 按关键词过滤
                if keyword and keyword not in service.get('service_name', ''):
                    continue

                # 验证服务数据完整性
                required_fields = ['service_id', 'service_name', 'service_kind', 'service_price']
                if not all(field in service for field in required_fields):
                    continue

                # 添加统计信息
                service_detail = service.copy()
                order_count = sum(1 for order in orders_db.values() if order['service_id'] == service['service_id'])
                service_detail['order_count'] = order_count

                # 计算平均评分
                evaluations = [e for e in evaluations_db.values()
                              if e['order_id'] in orders_db and orders_db[e['order_id']]['service_id'] == service['service_id']]
                if evaluations:
                    avg_rating = sum(e['evaluation_star'] for e in evaluations) / len(evaluations)
                    service_detail['average_rating'] = round(avg_rating, 1)
                else:
                    service_detail['average_rating'] = 0

                merchant_services.append(service_detail)
            except Exception as e:
                # 记录错误但继续处理其他服务
                print(f"处理商家服务数据时出错: {e}")
                continue

        # 按服务ID倒序排序（因为数据库表中没有created_at字段）
        merchant_services.sort(key=lambda x: x['service_id'], reverse=True)

        # 计算分页
        total = len(merchant_services)

        # 检查页码是否超出范围
        max_page = (total + limit - 1) // limit if total > 0 else 1
        if page > max_page and total > 0:
            return error_response(31013, f'页码超出范围，最大页码为{max_page}')

        start = (page - 1) * limit
        end = start + limit
        services = merchant_services[start:end]

        return success_response({
            'total': total,
            'page': page,
            'limit': limit,
            'max_page': max_page,
            'services': services
        }, '获取成功')

    except Exception as e:
        # 捕获所有未预期的错误
        print(f"获取商家服务列表时发生未知错误: {e}")
        return error_response(31099, '服务暂时不可用，请稍后重试')

@app.route('/api/merchant/services/<service_id>', methods=['PUT'])
@token_required
def update_merchant_service(service_id):
    """编辑服务"""
    if request.current_user['user_type'] != 'merchant':
        return error_response(403, '权限不足')

    merchant_id = request.current_user['user_id']
    data = request.get_json()

    if service_id not in services_db:
        return error_response(30001, '服务不存在')

    service = services_db[service_id]
    if service['merchant_id'] != merchant_id:
        return error_response(403, '无权编辑此服务')

    # 更新服务信息
    if 'service_name' in data:
        service['service_name'] = data['service_name']

    if 'service_kind' in data:
        service['service_kind'] = int(data['service_kind'])

    if 'service_description' in data:
        service['service_description'] = data['service_description']

    if 'service_price' in data:
        service['service_price'] = float(data['service_price'])

    if 'service_time' in data:
        service['service_time'] = int(data['service_time'])

    if 'service_picture' in data:
        service['service_picture'] = data['service_picture']

    service['updated_at'] = datetime.now().isoformat()

    return success_response(service, '服务更新成功')

@app.route('/api/merchant/services/<service_id>/offline', methods=['PUT'])
@token_required
def offline_service(service_id):
    """下架服务"""
    try:
        if request.current_user['user_type'] != 'merchant':
            return error_response(403, '权限不足')

        merchant_id = request.current_user['user_id']

        # 验证服务ID格式
        import re
        if not re.match(r'^S\d{10}$', service_id):
            return error_response(30016, '服务ID格式无效，应为S+10位数字')

        if service_id not in services_db:
            return error_response(30001, '服务不存在')

        service = services_db[service_id]
        if service.get('merchant_id') != merchant_id:
            return error_response(403, '无权操作此服务')

        # 检查服务是否已经下架
        if not service.get('is_active', True):
            return error_response(32005, '服务已经是下架状态')

        # 检查是否有未完成的订单
        active_orders = [order for order in orders_db.values()
                        if order['service_id'] == service_id and order['order_status'] in [1, 2]]

        if active_orders:
            return error_response(32002, f'存在{len(active_orders)}个未完成的订单，无法下架服务')

        # 下架服务
        service['is_active'] = False
        service['updated_at'] = datetime.now().isoformat()

        return success_response({
            'service_id': service_id,
            'service_name': service['service_name'],
            'updated_at': service['updated_at']
        }, '服务下架成功')

    except Exception as e:
        print(f"下架服务时发生错误: {e}")
        return error_response(32099, '下架失败，请稍后重试')

# 移除原删除接口，替换为下架功能


# 商家订单管理模块
@app.route('/api/merchant/orders', methods=['GET'])
@token_required
def get_merchant_orders():
    """获取商家订单列表"""
    if request.current_user['user_type'] != 'merchant':
        return error_response(403, '权限不足')

    merchant_id = request.current_user['user_id']
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 10))
    order_status = request.args.get('order_status')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    keyword = request.args.get('keyword', '')

    # 过滤商家订单（现在order表包含merchant_id字段）
    merchant_orders = []
    for order in orders_db.values():
        # 只显示当前商家的订单
        if order.get('merchant_id') != merchant_id:
            continue

        # 按状态过滤
        if order_status is not None and str(order['order_status']) != str(order_status):
            continue

        # 跳过日期过滤（因为数据库表中没有created_at字段）

        # 添加关联信息
        order_detail = order.copy()

        # 用户信息
        if order['user_id'] in users_db:
            user = users_db[order['user_id']]
            order_detail['user_phone'] = user['user_phone']

        # 服务信息
        if order['service_id'] in services_db:
            service = services_db[order['service_id']]
            order_detail['service_name'] = service['service_name']

        # 宠物信息
        if order['pet_id'] in pets_db:
            pet = pets_db[order['pet_id']]
            order_detail.update({
                'pet_kind': pet['pet_kind'],
                'pet_weight': pet['pet_weight']
            })

        # 预约信息
        for appointment in appointments_db.values():
            if appointment['order_id'] == order['order_id']:
                order_detail.update({
                    'appointment_time': appointment['appointment_time'],
                    'appointment_location': appointment['appointment_location']
                })
                break

        # 按关键词过滤（用户手机号、订单号）
        if keyword:
            if (keyword not in order_detail.get('user_phone', '') and
                keyword not in order['order_id']):
                continue

        merchant_orders.append(order_detail)

    # 按订单ID倒序排序（因为数据库表中没有created_at字段）
    merchant_orders.sort(key=lambda x: x['order_id'], reverse=True)

    # 分页
    total = len(merchant_orders)
    start = (page - 1) * limit
    end = start + limit
    orders = merchant_orders[start:end]

    return success_response({
        'total': total,
        'page': page,
        'limit': limit,
        'orders': orders
    }, '获取成功')

@app.route('/api/merchant/orders/<order_id>', methods=['GET'])
@token_required
def get_merchant_order_detail(order_id):
    """获取商家订单详情"""
    try:
        if request.current_user['user_type'] != 'merchant':
            return error_response(403, '权限不足')

        merchant_id = request.current_user['user_id']

        # 验证订单ID格式
        import re
        if not re.match(r'^O\d{10}$', order_id):
            return error_response(40016, '订单ID格式无效，应为O+10位数字')

        if order_id not in orders_db:
            return error_response(40001, '订单不存在')

        order = orders_db[order_id]
        if order.get('merchant_id') != merchant_id:
            return error_response(403, '无权查看此订单')

        # 构建详细订单信息
        order_detail = order.copy()

        # 添加用户信息
        if order['user_id'] in users_db:
            user = users_db[order['user_id']]
            order_detail['user_info'] = {
                'user_id': user['user_id'],
                'user_phone': user['user_phone'],
                'user_name': user.get('user_name', ''),
                'user_sex': user.get('user_sex')
            }

        # 添加服务信息
        if order['service_id'] in services_db:
            service = services_db[order['service_id']]
            order_detail['service_info'] = {
                'service_id': service['service_id'],
                'service_name': service['service_name'],
                'service_kind': service['service_kind'],
                'service_description': service.get('service_description', ''),
                'service_time': service['service_time'],
                'service_picture': service.get('service_picture', '')
            }

        # 添加宠物信息
        if order['pet_id'] in pets_db:
            pet = pets_db[order['pet_id']]
            order_detail['pet_info'] = {
                'pet_id': pet['pet_id'],
                'pet_kind': pet['pet_kind'],
                'pet_weight': pet['pet_weight'],
                'pet_age': pet.get('pet_age')
            }

        # 添加预约信息
        appointment = None
        for appt in appointments_db.values():
            if appt['order_id'] == order_id:
                appointment = appt
                break

        if appointment:
            order_detail['appointment_info'] = {
                'appointment_id': appointment['appointment_id'],
                'appointment_time': appointment['appointment_time'],
                'appointment_location': appointment['appointment_location'],
                'appointment_status': appointment['appointment_status'],
                'merchant_notes': appointment.get('merchant_notes', '')
            }

        # 添加评价信息
        evaluation = None
        for eval in evaluations_db.values():
            if eval['order_id'] == order_id:
                evaluation = eval
                break

        if evaluation:
            order_detail['evaluation_info'] = {
                'evaluation_id': evaluation['evaluation_id'],
                'evaluation_star': evaluation['evaluation_star'],
                'evaluation_context': evaluation['evaluation_context'],
                'evaluation_picture': evaluation.get('evaluation_picture', '')
            }

        return success_response(order_detail, '获取成功')

    except Exception as e:
        print(f"获取订单详情时发生错误: {e}")
        return error_response(40099, '获取失败，请稍后重试')

@app.route('/api/merchant/orders/<order_id>/confirm', methods=['PUT'])
@token_required
def confirm_merchant_order(order_id):
    """确认订单"""
    if request.current_user['user_type'] != 'merchant':
        return error_response(403, '权限不足')

    merchant_id = request.current_user['user_id']

    if order_id not in orders_db:
        return error_response(40001, '订单不存在')

    order = orders_db[order_id]
    if order['merchant_id'] != merchant_id:
        return error_response(403, '无权操作此订单')

    if order['order_status'] != 2:  # 必须是已预约状态
        return error_response(40002, '订单状态不允许确认')

    # 确认订单（这里可以添加更多业务逻辑）
    order['updated_at'] = datetime.now().isoformat()

    return success_response({
        'order_id': order_id,
        'order_status': order['order_status']
    }, '订单确认成功')

@app.route('/api/merchant/orders/<order_id>/complete', methods=['PUT'])
@token_required
def complete_merchant_order(order_id):
    """完成订单"""
    if request.current_user['user_type'] != 'merchant':
        return error_response(403, '权限不足')

    merchant_id = request.current_user['user_id']

    if order_id not in orders_db:
        return error_response(40001, '订单不存在')

    order = orders_db[order_id]
    if order['merchant_id'] != merchant_id:
        return error_response(403, '无权操作此订单')

    if order['order_status'] != 2:  # 必须是已预约状态
        return error_response(40002, '订单状态不允许完成')

    # 完成订单
    order['order_status'] = 3  # 已完成
    order['updated_at'] = datetime.now().isoformat()

    return success_response({
        'order_id': order_id,
        'order_status': 3
    }, '订单完成')

@app.route('/api/merchant/orders/<order_id>/cancel', methods=['PUT'])
@token_required
def cancel_merchant_order(order_id):
    """商家取消订单"""
    if request.current_user['user_type'] != 'merchant':
        return error_response(403, '权限不足')

    merchant_id = request.current_user['user_id']
    data = request.get_json()

    if order_id not in orders_db:
        return error_response(40001, '订单不存在')

    order = orders_db[order_id]
    if order['merchant_id'] != merchant_id:
        return error_response(403, '无权操作此订单')

    if order['order_status'] in [0, 3]:  # 已取消或已完成
        return error_response(40002, '订单状态不允许取消')

    # 取消订单
    order['order_status'] = 0  # 已取消
    order['cancel_reason'] = data.get('cancel_reason', '商家取消')
    order['updated_at'] = datetime.now().isoformat()

    # 计算补偿金额（120%）
    compensation_rate = 1.2
    refund_amount = order['order_price'] * compensation_rate

    return success_response({
        'order_id': order_id,
        'order_status': 0,
        'refund_amount': refund_amount,
        'compensation_rate': compensation_rate
    }, '订单取消成功')

# 商家预约管理模块
@app.route('/api/merchant/appointments', methods=['GET'])
@token_required
def get_merchant_appointments():
    """获取商家预约列表"""
    if request.current_user['user_type'] != 'merchant':
        return error_response(403, '权限不足')

    merchant_id = request.current_user['user_id']
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 10))
    appointment_status = request.args.get('appointment_status')
    date = request.args.get('date')
    keyword = request.args.get('keyword', '')

    # 过滤商家预约
    merchant_appointments = []
    for appointment in appointments_db.values():
        if appointment.get('merchant_id') != merchant_id:
            continue

        # 按状态过滤
        if appointment_status is not None and str(appointment['appointment_status']) != str(appointment_status):
            continue

        # 按日期过滤
        if date and not appointment['appointment_time'].startswith(date):
            continue

        # 添加关联信息
        appointment_detail = appointment.copy()

        # 订单信息
        if appointment['order_id'] in orders_db:
            order = orders_db[appointment['order_id']]

            # 用户信息
            if order['user_id'] in users_db:
                user = users_db[order['user_id']]
                appointment_detail['user_phone'] = user['user_phone']

            # 服务信息
            if order['service_id'] in services_db:
                service = services_db[order['service_id']]
                appointment_detail['service_name'] = service['service_name']

            # 宠物信息
            if order['pet_id'] in pets_db:
                pet = pets_db[order['pet_id']]
                appointment_detail.update({
                    'pet_kind': pet['pet_kind'],
                    'pet_weight': pet['pet_weight']
                })

        # 按关键词过滤
        if keyword:
            if (keyword not in appointment_detail.get('user_phone', '') and
                keyword not in appointment_detail.get('service_name', '')):
                continue

        merchant_appointments.append(appointment_detail)

    # 按预约时间排序
    merchant_appointments.sort(key=lambda x: x['appointment_time'])

    # 分页
    total = len(merchant_appointments)
    start = (page - 1) * limit
    end = start + limit
    appointments = merchant_appointments[start:end]

    return success_response({
        'total': total,
        'page': page,
        'limit': limit,
        'appointments': appointments
    }, '获取成功')

@app.route('/api/merchant/appointments/<appointment_id>', methods=['GET'])
@token_required
def get_merchant_appointment_detail(appointment_id):
    """获取商家预约详情"""
    try:
        if request.current_user['user_type'] != 'merchant':
            return error_response(403, '权限不足')

        merchant_id = request.current_user['user_id']

        # 验证预约ID格式
        import re
        if not re.match(r'^A\d{10}$', appointment_id):
            return error_response(50016, '预约ID格式无效，应为A+10位数字')

        if appointment_id not in appointments_db:
            return error_response(50002, '预约不存在')

        appointment = appointments_db[appointment_id]
        if appointment.get('merchant_id') != merchant_id:
            return error_response(403, '无权查看此预约')

        # 构建详细预约信息
        appointment_detail = appointment.copy()

        # 添加订单信息
        if appointment['order_id'] in orders_db:
            order = orders_db[appointment['order_id']]
            appointment_detail['order_info'] = {
                'order_id': order['order_id'],
                'order_status': order['order_status'],
                'order_price': order['order_price']
            }

            # 添加用户信息
            if order['user_id'] in users_db:
                user = users_db[order['user_id']]
                appointment_detail['user_info'] = {
                    'user_id': user['user_id'],
                    'user_phone': user['user_phone'],
                    'user_name': user.get('user_name', ''),
                    'user_sex': user.get('user_sex')
                }

            # 添加服务信息
            if order['service_id'] in services_db:
                service = services_db[order['service_id']]
                appointment_detail['service_info'] = {
                    'service_id': service['service_id'],
                    'service_name': service['service_name'],
                    'service_kind': service['service_kind'],
                    'service_description': service.get('service_description', ''),
                    'service_time': service['service_time'],
                    'service_price': service['service_price']
                }

            # 添加宠物信息
            if order['pet_id'] in pets_db:
                pet = pets_db[order['pet_id']]
                appointment_detail['pet_info'] = {
                    'pet_id': pet['pet_id'],
                    'pet_kind': pet['pet_kind'],
                    'pet_weight': pet['pet_weight'],
                    'pet_age': pet.get('pet_age')
                }

        return success_response(appointment_detail, '获取成功')

    except Exception as e:
        print(f"获取预约详情时发生错误: {e}")
        return error_response(50099, '获取失败，请稍后重试')

@app.route('/api/merchant/appointments/<appointment_id>/confirm', methods=['PUT'])
@token_required
def confirm_merchant_appointment(appointment_id):
    """确认预约"""
    try:
        if request.current_user['user_type'] != 'merchant':
            return error_response(403, '权限不足')

        merchant_id = request.current_user['user_id']
        data = request.get_json() or {}

        # 验证预约ID格式
        import re
        if not re.match(r'^A\d{10}$', appointment_id):
            return error_response(50016, '预约ID格式无效，应为A+10位数字')

        if appointment_id not in appointments_db:
            return error_response(50002, '预约不存在')

        appointment = appointments_db[appointment_id]
        if appointment.get('merchant_id') != merchant_id:
            return error_response(403, '无权操作此预约')

        if appointment['appointment_status'] != 0:  # 必须是待确认状态
            return error_response(50003, '预约状态不允许确认')

        # 确认预约
        appointment['appointment_status'] = 1  # 已确认
        appointment['merchant_notes'] = data.get('notes', '')
        appointment['updated_at'] = datetime.now().isoformat()

        return success_response({
            'appointment_id': appointment_id,
            'appointment_status': 1,
            'notes': appointment['merchant_notes'],
            'updated_at': appointment['updated_at']
        }, '预约确认成功')

    except Exception as e:
        print(f"确认预约时发生错误: {e}")
        return error_response(50099, '确认失败，请稍后重试')

@app.route('/api/merchant/appointments/<appointment_id>', methods=['PUT'])
@token_required
def update_merchant_appointment(appointment_id):
    """修改预约"""
    if request.current_user['user_type'] != 'merchant':
        return error_response(403, '权限不足')

    merchant_id = request.current_user['user_id']
    data = request.get_json()

    if appointment_id not in appointments_db:
        return error_response(50002, '预约不存在')

    appointment = appointments_db[appointment_id]
    if appointment['merchant_id'] != merchant_id:
        return error_response(403, '无权操作此预约')

    if appointment['appointment_status'] in [2, 3]:  # 已取消或已完成
        return error_response(40002, '预约状态不允许修改')

    # 更新预约信息
    if 'appointment_time' in data:
        appointment['appointment_time'] = data['appointment_time']

    if 'appointment_location' in data:
        appointment['appointment_location'] = data['appointment_location']

    if 'notes' in data:
        appointment['merchant_notes'] = data['notes']

    appointment['updated_at'] = datetime.now().isoformat()

    return success_response({
        'appointment_id': appointment_id,
        'appointment_time': appointment['appointment_time'],
        'appointment_location': appointment['appointment_location'],
        'notes': appointment.get('merchant_notes', '')
    }, '预约修改成功')

@app.route('/api/merchant/appointments/<appointment_id>/cancel', methods=['PUT'])
@token_required
def cancel_merchant_appointment(appointment_id):
    """商家取消预约"""
    if request.current_user['user_type'] != 'merchant':
        return error_response(403, '权限不足')

    merchant_id = request.current_user['user_id']
    data = request.get_json()

    if appointment_id not in appointments_db:
        return error_response(50002, '预约不存在')

    appointment = appointments_db[appointment_id]
    if appointment['merchant_id'] != merchant_id:
        return error_response(403, '无权操作此预约')

    # 取消预约
    appointment['appointment_status'] = 2  # 已取消
    appointment['cancel_reason'] = data.get('cancel_reason', '商家取消')
    appointment['updated_at'] = datetime.now().isoformat()

    # 计算补偿
    compensation_rate = data.get('compensation_rate', 1.2)

    # 获取订单信息计算退款
    order = orders_db.get(appointment['order_id'])
    refund_amount = 0
    if order:
        refund_amount = order['order_price'] * compensation_rate

    return success_response({
        'appointment_id': appointment_id,
        'appointment_status': 2,
        'refund_amount': refund_amount,
        'compensation_rate': compensation_rate
    }, '预约取消成功')

# 商家数据统计模块
@app.route('/api/merchant/statistics', methods=['GET'])
@token_required
def get_merchant_statistics():
    """获取商家统计数据"""
    if request.current_user['user_type'] != 'merchant':
        return error_response(403, '权限不足')

    merchant_id = request.current_user['user_id']
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    # 过滤商家订单
    merchant_orders = [order for order in orders_db.values() if order.get('merchant_id') == merchant_id]

    # 跳过日期过滤（因为数据库表中没有created_at字段）
    # if start_date:
    #     merchant_orders = [order for order in merchant_orders if order['created_at'] >= start_date]
    # if end_date:
    #     merchant_orders = [order for order in merchant_orders if order['created_at'] <= end_date]

    # 统计数据
    total_orders = len(merchant_orders)
    completed_orders = len([order for order in merchant_orders if order['order_status'] == 3])
    cancelled_orders = len([order for order in merchant_orders if order['order_status'] == 0])
    pending_orders = len([order for order in merchant_orders if order['order_status'] in [1, 2]])

    # 计算总收入
    total_revenue = sum(order['order_price'] for order in merchant_orders if order['order_status'] == 3)

    # 计算平均评分
    merchant_evaluations = [e for e in evaluations_db.values() if e.get('merchant_id') == merchant_id]
    average_rating = 0
    if merchant_evaluations:
        total_stars = sum(e['evaluation_star'] for e in merchant_evaluations)
        average_rating = round(total_stars / len(merchant_evaluations), 1)

    # 服务统计
    service_statistics = []
    merchant_services = [s for s in services_db.values() if s.get('merchant_id') == merchant_id]

    for service in merchant_services:
        service_orders = [order for order in merchant_orders if order['service_id'] == service['service_id']]
        service_revenue = sum(order['order_price'] for order in service_orders if order['order_status'] == 3)

        service_evaluations = [e for e in merchant_evaluations if e.get('service_id') == service['service_id']]
        service_rating = 0
        if service_evaluations:
            service_rating = round(sum(e['evaluation_star'] for e in service_evaluations) / len(service_evaluations), 1)

        service_statistics.append({
            'service_name': service['service_name'],
            'order_count': len(service_orders),
            'revenue': service_revenue,
            'average_rating': service_rating
        })

    return success_response({
        'total_orders': total_orders,
        'completed_orders': completed_orders,
        'cancelled_orders': cancelled_orders,
        'pending_orders': pending_orders,
        'total_revenue': total_revenue,
        'average_rating': average_rating,
        'service_statistics': service_statistics
    }, '获取成功')

# ==================== 通用工具接口 ====================

@app.route('/api/upload', methods=['POST'])
@token_required
def upload_file():
    """文件上传"""
    if 'file' not in request.files:
        return error_response(70001, '没有文件')

    file = request.files['file']
    file_type = request.form.get('type', 'general')

    if file.filename == '':
        return error_response(70001, '没有选择文件')

    # 检查文件类型
    allowed_extensions = {'jpg', 'jpeg', 'png', 'gif'}
    if not ('.' in file.filename and file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
        return error_response(70002, '文件格式不支持')

    # 检查文件大小（5MB）
    file.seek(0, 2)  # 移动到文件末尾
    file_size = file.tell()
    file.seek(0)  # 重置到文件开头

    if file_size > 5 * 1024 * 1024:
        return error_response(70003, '文件大小超限')

    # 模拟文件上传（实际项目中应上传到云存储）
    filename = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{file.filename}"
    file_url = f"https://cdn.petservice.com/images/{filename}"

    return success_response({
        'file_url': file_url,
        'file_size': file_size,
        'file_type': file.content_type
    }, '上传成功')

@app.route('/api/available-times', methods=['GET'])
def get_available_times():
    """获取可用时间段"""
    date = request.args.get('date')
    service_id = request.args.get('service_id')
    location = request.args.get('location')

    if not date:
        return error_response(400, '日期参数不能为空')

    # 模拟可用时间段（实际项目中应根据商家营业时间和已预约时间计算）
    all_times = ['09:00', '10:00', '11:00', '14:00', '15:00', '16:00', '17:00', '18:00']

    # 查找该日期已预约的时间
    booked_times = []
    for appointment in appointments_db.values():
        if appointment['appointment_time'].startswith(date):
            time_part = appointment['appointment_time'].split('T')[1][:5]
            booked_times.append(time_part)

    # 过滤已预约的时间
    available_times = [time for time in all_times if time not in booked_times]

    return success_response({
        'date': date,
        'available_times': available_times
    }, '获取成功')

if __name__ == '__main__':
    # 初始化一些测试数据
    print("正在初始化测试数据...")
    
    # 添加测试商家（严格按照数据库表结构）
    merchant_id = generate_id('M')
    merchants_db[merchant_id] = {
        'merchant_id': merchant_id,
        'merchant_account': 'petshop001',
        'merchant_password': hash_password('123456'),
        'merchant_name': '爱宠美容店',
        'merchant_phone': '***********',
        'merchant_location': '北京市朝阳区xxx街道'
    }

    # 添加测试服务（严格按照数据库表结构，包含merchant_id）
    service_id = generate_id('S', 11)
    services_db[service_id] = {
        'service_id': service_id,
        'merchant_id': merchant_id,  # 关联到测试商家
        'service_name': '宠物基础美容套餐',
        'service_kind': 0,
        'service_description': '包含洗澡、修剪毛发、清洁耳朵等服务',
        'service_price': 88.00,
        'service_time': 120,
        'service_picture': 'url1,url2'
    }

    # 添加测试用户
    test_user_id = generate_id('U')
    users_db[test_user_id] = {
        'user_id': test_user_id,
        'user_phone': '***********',
        'user_verification_code': None,
        'user_name': '测试用户',
        'user_sex': 0
    }

    # 添加测试宠物
    test_pet_id = generate_id('P')
    pets_db[test_pet_id] = {
        'pet_id': test_pet_id,
        'user_id': test_user_id,
        'pet_kind': '狗',
        'pet_age': '2023-01-01',
        'pet_weight': '5.5'
    }

    # 添加测试订单
    test_order_id = generate_id('O', 11)
    orders_db[test_order_id] = {
        'order_id': test_order_id,
        'user_id': test_user_id,
        'merchant_id': merchant_id,
        'service_id': service_id,
        'order_status': 2,  # 已预约状态
        'order_price': 88.00,
        'pet_id': test_pet_id
    }

    # 添加测试预约
    test_appointment_id = generate_id('A', 11)
    appointments_db[test_appointment_id] = {
        'appointment_id': test_appointment_id,
        'order_id': test_order_id,
        'appointment_time': '2024-12-25 10:00:00',
        'appointment_location': '北京市朝阳区xxx街道',
        'appointment_status': 0,  # 待确认状态
        'user_id': test_user_id,
        'merchant_id': merchant_id
    }
    
    print(f"测试商家账号: petshop001, 密码: 123456")
    print(f"测试服务ID: {service_id}")
    print(f"测试订单ID: {test_order_id}")
    print(f"测试预约ID: {test_appointment_id}")
    print("Flask 应用启动成功！")
    print("API 基础地址: http://localhost:5000/api")
    print("可以在 APIFOX 中测试以下接口:")
    print("- POST /api/auth/send-code")
    print("- POST /api/auth/register")
    print("- POST /api/auth/login")
    print("- PUT /api/user/profile")
    print("- POST /api/pets")
    print("- GET /api/pets")
    print("- DELETE /api/pets/{pet_id}")
    print(f"- PUT /api/merchant/appointments/{test_appointment_id}/confirm")
    print(f"- GET /api/merchant/orders/{test_order_id}")
    print(f"- GET /api/merchant/appointments/{test_appointment_id}")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
