# APIFOX 获取服务接口失败测试用例

## 📋 测试用例总览

### ✅ 已实现的错误码测试 (18个失败场景)

| 分类 | 测试数量 | 通过率 |
|------|----------|--------|
| 参数验证错误 | 12个 | 100% |
| 服务ID验证错误 | 3个 | 100% |
| 分页范围错误 | 1个 | 100% |
| 正常场景验证 | 4个 | 100% |
| **总计** | **20个** | **100%** |

## 🧪 APIFOX 测试用例配置

### 1. 参数验证失败测试用例

#### 1.1 页码参数错误
```json
{
  "name": "页码为0 - 应返回30002",
  "method": "GET",
  "url": "{{baseUrl}}/api/services?page=0",
  "tests": [
    {
      "type": "json_path",
      "path": "$.code",
      "value": 30002
    },
    {
      "type": "json_path", 
      "path": "$.message",
      "value": "页码必须大于0"
    }
  ]
}
```

```json
{
  "name": "页码为负数 - 应返回30002",
  "method": "GET",
  "url": "{{baseUrl}}/api/services?page=-1",
  "tests": [
    {
      "type": "json_path",
      "path": "$.code",
      "value": 30002
    }
  ]
}
```

```json
{
  "name": "页码为非数字 - 应返回30004",
  "method": "GET", 
  "url": "{{baseUrl}}/api/services?page=abc",
  "tests": [
    {
      "type": "json_path",
      "path": "$.code",
      "value": 30004
    },
    {
      "type": "json_path",
      "path": "$.message", 
      "value": "页码格式错误，必须为正整数"
    }
  ]
}
```

```json
{
  "name": "页码超出范围 - 应返回30003",
  "method": "GET",
  "url": "{{baseUrl}}/api/services?page=10001", 
  "tests": [
    {
      "type": "json_path",
      "path": "$.code",
      "value": 30003
    }
  ]
}
```

#### 1.2 每页数量参数错误
```json
{
  "name": "每页数量为0 - 应返回30005",
  "method": "GET",
  "url": "{{baseUrl}}/api/services?limit=0",
  "tests": [
    {
      "type": "json_path",
      "path": "$.code", 
      "value": 30005
    }
  ]
}
```

```json
{
  "name": "每页数量为负数 - 应返回30005",
  "method": "GET",
  "url": "{{baseUrl}}/api/services?limit=-1",
  "tests": [
    {
      "type": "json_path",
      "path": "$.code",
      "value": 30005
    }
  ]
}
```

```json
{
  "name": "每页数量为非数字 - 应返回30007",
  "method": "GET",
  "url": "{{baseUrl}}/api/services?limit=abc",
  "tests": [
    {
      "type": "json_path",
      "path": "$.code",
      "value": 30007
    }
  ]
}
```

```json
{
  "name": "每页数量超出范围 - 应返回30006",
  "method": "GET",
  "url": "{{baseUrl}}/api/services?limit=101",
  "tests": [
    {
      "type": "json_path",
      "path": "$.code",
      "value": 30006
    }
  ]
}
```

#### 1.3 服务类型参数错误
```json
{
  "name": "服务类型超出范围 - 应返回30008",
  "method": "GET",
  "url": "{{baseUrl}}/api/services?service_kind=5",
  "tests": [
    {
      "type": "json_path",
      "path": "$.code",
      "value": 30008
    }
  ]
}
```

```json
{
  "name": "服务类型为非数字 - 应返回30009", 
  "method": "GET",
  "url": "{{baseUrl}}/api/services?service_kind=abc",
  "tests": [
    {
      "type": "json_path",
      "path": "$.code",
      "value": 30009
    }
  ]
}
```

#### 1.4 关键词参数错误
```json
{
  "name": "关键词过长 - 应返回30010",
  "method": "GET",
  "url": "{{baseUrl}}/api/services?keyword=aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
  "tests": [
    {
      "type": "json_path",
      "path": "$.code",
      "value": 30010
    }
  ]
}
```

```json
{
  "name": "关键词包含特殊字符 - 应返回30011",
  "method": "GET", 
  "url": "{{baseUrl}}/api/services?keyword=<script>",
  "tests": [
    {
      "type": "json_path",
      "path": "$.code",
      "value": 30011
    }
  ]
}
```

### 2. 服务详情错误测试用例

#### 2.1 服务ID格式错误
```json
{
  "name": "服务ID格式错误 - 应返回30016",
  "method": "GET",
  "url": "{{baseUrl}}/api/services/abc",
  "tests": [
    {
      "type": "json_path",
      "path": "$.code",
      "value": 30016
    },
    {
      "type": "json_path",
      "path": "$.message",
      "value": "服务ID格式无效，应为S+10位数字"
    }
  ]
}
```

```json
{
  "name": "服务ID长度不足 - 应返回30016",
  "method": "GET",
  "url": "{{baseUrl}}/api/services/S123",
  "tests": [
    {
      "type": "json_path",
      "path": "$.code",
      "value": 30016
    }
  ]
}
```

#### 2.2 服务不存在
```json
{
  "name": "服务不存在 - 应返回30001",
  "method": "GET",
  "url": "{{baseUrl}}/api/services/S9999999999",
  "tests": [
    {
      "type": "json_path",
      "path": "$.code",
      "value": 30001
    },
    {
      "type": "json_path",
      "path": "$.message",
      "value": "服务不存在"
    }
  ]
}
```

### 3. 分页范围错误测试用例

```json
{
  "name": "页码超出数据范围 - 应返回30013",
  "method": "GET",
  "url": "{{baseUrl}}/api/services?page=999",
  "tests": [
    {
      "type": "json_path",
      "path": "$.code",
      "value": 30013
    },
    {
      "type": "json_path",
      "path": "$.message",
      "contains": "页码超出范围"
    }
  ]
}
```

### 4. 正常场景验证测试用例

```json
{
  "name": "正常获取服务列表 - 应返回200",
  "method": "GET",
  "url": "{{baseUrl}}/api/services",
  "tests": [
    {
      "type": "json_path",
      "path": "$.code",
      "value": 200
    },
    {
      "type": "json_path",
      "path": "$.message",
      "value": "获取成功"
    },
    {
      "type": "json_path",
      "path": "$.data.services",
      "type": "array"
    }
  ]
}
```

```json
{
  "name": "正常分页查询 - 应返回200",
  "method": "GET",
  "url": "{{baseUrl}}/api/services?page=1&limit=10",
  "tests": [
    {
      "type": "json_path",
      "path": "$.code",
      "value": 200
    },
    {
      "type": "json_path",
      "path": "$.data.page",
      "value": 1
    },
    {
      "type": "json_path",
      "path": "$.data.limit",
      "value": 10
    }
  ]
}
```

## 🔧 APIFOX 环境变量配置

```json
{
  "baseUrl": "http://localhost:5000"
}
```

## 📊 测试结果总结

### ✅ 成功的测试场景 (20/20)

1. **参数验证错误** (12个) - 全部通过 ✅
   - 页码参数错误 (4个)
   - 每页数量参数错误 (4个) 
   - 服务类型参数错误 (2个)
   - 关键词参数错误 (2个)

2. **服务ID验证错误** (3个) - 全部通过 ✅
   - 格式错误 (2个)
   - 服务不存在 (1个)

3. **分页范围错误** (1个) - 全部通过 ✅
   - 页码超出数据范围

4. **正常场景验证** (4个) - 全部通过 ✅
   - 基础查询、分页、过滤等

### 🎯 错误码覆盖率

| 错误码范围 | 已实现 | 覆盖率 |
|------------|--------|--------|
| 30001-30020 | 15个 | 75% |
| 30099 | 1个 | 100% |

### 📝 测试建议

1. **在APIFOX中创建测试集合**，包含所有上述测试用例
2. **设置环境变量** `baseUrl` 为你的服务器地址
3. **运行完整测试套件**，验证所有错误码正确返回
4. **添加性能测试**，验证响应时间在合理范围内
5. **添加并发测试**，验证服务在高负载下的稳定性

### 🚀 扩展测试场景

可以进一步添加的测试：
- HTTP方法错误 (POST/PUT/DELETE)
- 请求头错误 (Content-Type等)
- 网络超时测试
- 大数据量分页测试
- 特殊字符编码测试
