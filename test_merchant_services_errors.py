#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试商家获取服务接口的错误码返回
"""

import requests
import json

BASE_URL = "http://localhost:5000/api"

def get_merchant_token():
    """获取商家登录Token"""
    login_data = {
        "merchant_account": "petshop001",
        "merchant_password": "123456"
    }
    
    response = requests.post(f"{BASE_URL}/merchant/auth/login", json=login_data)
    if response.status_code == 200:
        data = response.json()
        return data['data']['token']
    else:
        print(f"商家登录失败: {response.text}")
        return None

def test_api_with_token(url, token, expected_code, description):
    """测试需要Token的API"""
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(url, headers=headers)
        
        # 检查是否返回JSON
        try:
            data = response.json()
            actual_code = data.get('code')
            message = data.get('message', '')
            
            # 检查HTTP状态码
            expected_http_status = 400 if expected_code >= 1000 else expected_code
            actual_http_status = response.status_code
            
            if actual_code == expected_code and actual_http_status == expected_http_status:
                print(f"✅ {description}")
                print(f"   URL: {url}")
                print(f"   Expected Code: {expected_code}, Got: {actual_code}")
                print(f"   Expected HTTP Status: {expected_http_status}, Got: {actual_http_status}")
                print(f"   Message: {message}")
            else:
                print(f"❌ {description}")
                print(f"   URL: {url}")
                print(f"   Expected Code: {expected_code}, Got: {actual_code}")
                print(f"   Expected HTTP Status: {expected_http_status}, Got: {actual_http_status}")
                print(f"   Message: {message}")
        except json.JSONDecodeError:
            print(f"❌ {description}")
            print(f"   URL: {url}")
            print(f"   Error: Response is not valid JSON")
            print(f"   HTTP Status: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
        print()
        
    except Exception as e:
        print(f"❌ {description}")
        print(f"   URL: {url}")
        print(f"   Error: {e}")
        print()

def test_api_without_token(url, expected_code, description):
    """测试不带Token的API"""
    try:
        response = requests.get(url)
        
        try:
            data = response.json()
            actual_code = data.get('code')
            message = data.get('message', '')
            
            expected_http_status = 400 if expected_code >= 1000 else expected_code
            actual_http_status = response.status_code
            
            if actual_code == expected_code and actual_http_status == expected_http_status:
                print(f"✅ {description}")
                print(f"   Expected Code: {expected_code}, Got: {actual_code}")
                print(f"   Expected HTTP Status: {expected_http_status}, Got: {actual_http_status}")
            else:
                print(f"❌ {description}")
                print(f"   Expected Code: {expected_code}, Got: {actual_code}")
                print(f"   Expected HTTP Status: {expected_http_status}, Got: {actual_http_status}")
        except json.JSONDecodeError:
            print(f"❌ {description}")
            print(f"   Error: Response is not valid JSON")
            print(f"   HTTP Status: {response.status_code}")
        print()
        
    except Exception as e:
        print(f"❌ {description}")
        print(f"   Error: {e}")
        print()

def main():
    print("🧪 测试商家获取服务接口错误码...")
    print("=" * 60)
    
    # 获取商家Token
    print("🔑 获取商家登录Token...")
    token = get_merchant_token()
    if not token:
        print("❌ 无法获取商家Token，测试终止")
        return
    print(f"✅ 成功获取Token: {token[:20]}...")
    print()
    
    # 测试无Token访问
    print("📋 测试无Token访问:")
    test_api_without_token(f"{BASE_URL}/merchant/services", 401, "无Token访问商家服务列表")
    
    # 测试参数验证错误
    print("📋 测试参数验证错误:")
    test_api_with_token(f"{BASE_URL}/merchant/services?page=0", token, 31002, "页码为0")
    test_api_with_token(f"{BASE_URL}/merchant/services?page=-1", token, 31002, "页码为负数")
    test_api_with_token(f"{BASE_URL}/merchant/services?page=abc", token, 31004, "页码为非数字")
    test_api_with_token(f"{BASE_URL}/merchant/services?page=10001", token, 31003, "页码超出范围")
    
    test_api_with_token(f"{BASE_URL}/merchant/services?limit=0", token, 31005, "每页数量为0")
    test_api_with_token(f"{BASE_URL}/merchant/services?limit=-1", token, 31005, "每页数量为负数")
    test_api_with_token(f"{BASE_URL}/merchant/services?limit=abc", token, 31007, "每页数量为非数字")
    test_api_with_token(f"{BASE_URL}/merchant/services?limit=101", token, 31006, "每页数量超出范围")
    
    test_api_with_token(f"{BASE_URL}/merchant/services?service_kind=5", token, 31008, "服务类型超出范围")
    test_api_with_token(f"{BASE_URL}/merchant/services?service_kind=abc", token, 31009, "服务类型为非数字")
    
    # 测试关键词验证
    long_keyword = "a" * 51  # 51个字符
    test_api_with_token(f"{BASE_URL}/merchant/services?keyword={long_keyword}", token, 31010, "关键词过长")
    test_api_with_token(f"{BASE_URL}/merchant/services?keyword=<script>", token, 31011, "关键词包含特殊字符")
    
    # 测试分页超出范围
    print("📋 测试分页超出范围:")
    test_api_with_token(f"{BASE_URL}/merchant/services?page=999", token, 31013, "页码超出数据范围")
    
    # 测试正常情况
    print("📋 测试正常情况:")
    test_api_with_token(f"{BASE_URL}/merchant/services", token, 200, "正常获取商家服务列表")
    test_api_with_token(f"{BASE_URL}/merchant/services?page=1&limit=10", token, 200, "正常分页查询")
    test_api_with_token(f"{BASE_URL}/merchant/services?service_kind=0", token, 200, "正常按类型查询")
    test_api_with_token(f"{BASE_URL}/merchant/services?keyword=美容", token, 200, "正常关键词搜索")

if __name__ == "__main__":
    main()
