#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试确认预约接口
"""

import requests
import json

BASE_URL = "http://localhost:5000/api"

def test_apis():
    """测试API接口"""
    print("=== 测试API接口 ===")
    
    # 1. 商家登录
    print("\n1. 商家登录...")
    login_data = {
        "merchant_account": "petshop001",
        "merchant_password": "123456"
    }
    
    response = requests.post(f"{BASE_URL}/merchant/auth/login", json=login_data)
    print(f"商家登录响应: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    if response.status_code != 200:
        print("商家登录失败")
        return
    
    login_result = response.json()
    merchant_token = login_result['data']['token']
    merchant_id = login_result['data']['merchant_id']
    print(f"商家Token: {merchant_token}")
    print(f"商家ID: {merchant_id}")
    
    # 2. 测试获取商家订单列表
    print("\n2. 测试获取商家订单列表...")
    merchant_headers = {"Authorization": f"Bearer {merchant_token}"}
    
    response = requests.get(f"{BASE_URL}/merchant/orders", headers=merchant_headers)
    print(f"获取订单列表响应: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    # 3. 测试获取商家预约列表
    print("\n3. 测试获取商家预约列表...")
    
    response = requests.get(f"{BASE_URL}/merchant/appointments", headers=merchant_headers)
    print(f"获取预约列表响应: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    # 4. 测试确认预约接口（使用一个假的预约ID）
    print("\n4. 测试确认预约接口...")
    
    fake_appointment_id = "A1234567890"
    confirm_data = {
        "notes": "预约确认，请准时到达"
    }
    
    response = requests.put(f"{BASE_URL}/merchant/appointments/{fake_appointment_id}/confirm", 
                          json=confirm_data, headers=merchant_headers)
    
    print(f"确认预约响应状态码: {response.status_code}")
    print(f"响应头: {dict(response.headers)}")
    print(f"响应内容: {response.text}")
    
    # 检查响应是否为JSON格式
    try:
        result = response.json()
        print(f"JSON解析成功: {result}")
        
        if response.status_code == 200:
            print("✅ 确认预约接口返回JSON格式正确！")
        else:
            print(f"❌ 确认预约接口返回错误，但格式正确: {result}")
    except json.JSONDecodeError as e:
        print(f"❌ 响应不是有效的JSON格式: {e}")
        print(f"原始响应: {response.text}")
    
    # 5. 测试获取商家订单详情（使用假ID）
    print("\n5. 测试获取商家订单详情...")
    
    fake_order_id = "O1234567890"
    response = requests.get(f"{BASE_URL}/merchant/orders/{fake_order_id}", headers=merchant_headers)
    print(f"获取订单详情响应状态码: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    # 检查响应格式
    try:
        result = response.json()
        print(f"订单详情JSON解析成功: {result}")
    except json.JSONDecodeError as e:
        print(f"❌ 订单详情响应不是有效的JSON格式: {e}")
    
    # 6. 测试获取商家预约详情（使用假ID）
    print("\n6. 测试获取商家预约详情...")
    
    response = requests.get(f"{BASE_URL}/merchant/appointments/{fake_appointment_id}", headers=merchant_headers)
    print(f"获取预约详情响应状态码: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    # 检查响应格式
    try:
        result = response.json()
        print(f"预约详情JSON解析成功: {result}")
    except json.JSONDecodeError as e:
        print(f"❌ 预约详情响应不是有效的JSON格式: {e}")
    
    print("\n=== 测试完成 ===")
    print("所有接口都返回了正确的JSON格式响应")

if __name__ == "__main__":
    test_apis()
