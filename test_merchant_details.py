#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试商家订单详情、预约详情和确认预约接口
"""

import requests
import json

BASE_URL = "http://localhost:5000/api"

def get_merchant_token():
    """获取商家登录Token"""
    login_data = {
        "merchant_account": "petshop001",
        "merchant_password": "123456"
    }
    
    response = requests.post(f"{BASE_URL}/merchant/auth/login", json=login_data)
    if response.status_code == 200:
        data = response.json()
        return data['data']['token']
    else:
        print(f"商家登录失败: {response.text}")
        return None

def get_user_token():
    """获取用户登录Token"""
    # 先发送验证码
    send_code_data = {"user_phone": "***********"}
    code_response = requests.post(f"{BASE_URL}/auth/send-code", json=send_code_data)

    if code_response.status_code != 200:
        print(f"发送验证码失败: {code_response.text}")
        return None

    # 使用固定验证码（从控制台输出中可以看到）
    verification_code = "123456"  # 默认验证码

    # 先尝试注册
    register_data = {
        "user_phone": "***********",
        "user_verification_code": verification_code
    }

    register_response = requests.post(f"{BASE_URL}/auth/register", json=register_data)
    if register_response.status_code == 200:
        data = register_response.json()
        return data['data']['token']

    # 如果注册失败（用户已存在），尝试登录
    login_data = {
        "user_phone": "***********",
        "user_verification_code": verification_code
    }

    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code == 200:
        data = response.json()
        return data['data']['token']
    else:
        print(f"用户登录失败: {response.text}")
        return None

def create_test_data(user_token, merchant_token):
    """创建测试数据"""
    headers_user = {"Authorization": f"Bearer {user_token}", "Content-Type": "application/json"}
    headers_merchant = {"Authorization": f"Bearer {merchant_token}", "Content-Type": "application/json"}
    
    # 1. 创建宠物
    pet_data = {
        "pet_name": "小白",
        "pet_kind": "金毛",
        "pet_weight": 25.5,
        "pet_age": 2
    }
    response = requests.post(f"{BASE_URL}/pets", headers=headers_user, json=pet_data)
    if response.status_code != 200:
        print(f"创建宠物失败: {response.text}")
        return None, None, None
    
    pet_id = response.json()['data']['pet_id']
    
    # 2. 获取服务列表
    response = requests.get(f"{BASE_URL}/services")
    if response.status_code != 200:
        print(f"获取服务失败: {response.text}")
        return None, None, None
    
    services = response.json()['data']['services']
    if not services:
        print("没有可用服务")
        return None, None, None
    
    service_id = services[0]['service_id']
    
    # 3. 创建订单
    order_data = {
        "service_id": service_id,
        "pet_id": pet_id
    }
    response = requests.post(f"{BASE_URL}/orders", headers=headers_user, json=order_data)
    if response.status_code != 200:
        print(f"创建订单失败: {response.text}")
        return None, None, None
    
    order_id = response.json()['data']['order_id']
    
    # 4. 模拟支付
    payment_data = {"payment_method": "alipay"}
    response = requests.post(f"{BASE_URL}/orders/{order_id}/pay", headers=headers_user, json=payment_data)
    if response.status_code != 200:
        print(f"支付失败: {response.text}")
        return None, None, None
    
    # 5. 创建预约
    appointment_data = {
        "order_id": order_id,
        "appointment_time": "2024-01-15T10:00:00",
        "appointment_location": "店内服务"
    }
    response = requests.post(f"{BASE_URL}/appointments", headers=headers_user, json=appointment_data)
    if response.status_code != 200:
        print(f"创建预约失败: {response.text}")
        return None, None, None
    
    appointment_id = response.json()['data']['appointment_id']
    
    return order_id, appointment_id, pet_id

def test_api(url, headers, expected_code, description, method="GET", data=None):
    """测试API接口"""
    try:
        if method == "GET":
            response = requests.get(url, headers=headers)
        elif method == "PUT":
            response = requests.put(url, headers=headers, json=data or {})
        else:
            response = requests.request(method, url, headers=headers, json=data or {})
        
        # 检查是否返回JSON
        try:
            result = response.json()
            actual_code = result.get('code')
            message = result.get('message', '')
            
            # 检查HTTP状态码
            expected_http_status = 400 if expected_code >= 1000 else expected_code
            actual_http_status = response.status_code
            
            if actual_code == expected_code and actual_http_status == expected_http_status:
                print(f"✅ {description}")
                print(f"   Expected Code: {expected_code}, Got: {actual_code}")
                print(f"   Expected HTTP Status: {expected_http_status}, Got: {actual_http_status}")
                print(f"   Message: {message}")
                if 'data' in result and result['data']:
                    print(f"   Data Keys: {list(result['data'].keys()) if isinstance(result['data'], dict) else 'Non-dict data'}")
            else:
                print(f"❌ {description}")
                print(f"   Expected Code: {expected_code}, Got: {actual_code}")
                print(f"   Expected HTTP Status: {expected_http_status}, Got: {actual_http_status}")
                print(f"   Message: {message}")
        except json.JSONDecodeError:
            print(f"❌ {description}")
            print(f"   Error: Response is not valid JSON")
            print(f"   HTTP Status: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
        print()
        
        return response
        
    except Exception as e:
        print(f"❌ {description}")
        print(f"   Error: {e}")
        print()
        return None

def main():
    print("🧪 测试商家订单详情、预约详情和确认预约接口...")
    print("=" * 60)
    
    # 获取Token
    print("🔑 获取登录Token...")
    merchant_token = get_merchant_token()
    user_token = get_user_token()
    
    if not merchant_token or not user_token:
        print("❌ 无法获取Token，测试终止")
        return
    
    print(f"✅ 成功获取Token")
    print()
    
    # 创建测试数据
    print("📋 创建测试数据...")
    order_id, appointment_id, pet_id = create_test_data(user_token, merchant_token)
    
    if not order_id or not appointment_id:
        print("❌ 创建测试数据失败，测试终止")
        return
    
    print(f"✅ 测试数据创建成功:")
    print(f"   Order ID: {order_id}")
    print(f"   Appointment ID: {appointment_id}")
    print(f"   Pet ID: {pet_id}")
    print()
    
    headers = {"Authorization": f"Bearer {merchant_token}"}
    
    # 测试商家获取订单详情
    print("📋 测试商家获取订单详情:")
    test_api(f"{BASE_URL}/merchant/orders/{order_id}", headers, 200, 
             f"获取订单详情: {order_id}")
    
    # 测试商家获取预约详情
    print("📋 测试商家获取预约详情:")
    test_api(f"{BASE_URL}/merchant/appointments/{appointment_id}", headers, 200, 
             f"获取预约详情: {appointment_id}")
    
    # 测试确认预约
    print("📋 测试确认预约:")
    confirm_data = {"notes": "已确认，请按时到店"}
    test_api(f"{BASE_URL}/merchant/appointments/{appointment_id}/confirm", headers, 200, 
             f"确认预约: {appointment_id}", method="PUT", data=confirm_data)
    
    # 测试错误场景
    print("📋 测试错误场景:")
    
    # 无效订单ID
    test_api(f"{BASE_URL}/merchant/orders/INVALID_ID", headers, 40016, 
             "无效订单ID格式")
    
    # 不存在的订单
    test_api(f"{BASE_URL}/merchant/orders/O9999999999", headers, 40001, 
             "不存在的订单")
    
    # 无效预约ID
    test_api(f"{BASE_URL}/merchant/appointments/INVALID_ID", headers, 50016, 
             "无效预约ID格式")
    
    # 不存在的预约
    test_api(f"{BASE_URL}/merchant/appointments/A9999999999", headers, 50002, 
             "不存在的预约")
    
    # 重复确认预约
    test_api(f"{BASE_URL}/merchant/appointments/{appointment_id}/confirm", headers, 50003, 
             "重复确认已确认的预约", method="PUT", data={})
    
    print("🎉 测试完成！")

if __name__ == "__main__":
    main()
