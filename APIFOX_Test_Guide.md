# APIFOX 测试指南

## 启动应用

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 运行应用：
```bash
python app.py
```

3. 应用将在 `http://localhost:5000` 启动

## APIFOX 测试配置

### 基础配置
- **Base URL**: `http://localhost:5000/api`
- **Content-Type**: `application/json`

### 响应格式说明
所有API响应都采用统一格式：
```json
{
  "code": 200,
  "data": {},
  "message": "操作成功"
}
```
**注意**: 响应中不包含timestamp字段，顺序为code、data、message

### 环境变量设置
在 APIFOX 中设置以下环境变量：
- `base_url`: `http://localhost:5000/api`
- `user_token`: 用户登录后获取的token
- `merchant_token`: 商家登录后获取的token

## 测试流程

### 1. 用户端测试流程

#### 步骤1: 发送验证码
```
POST {{base_url}}/auth/send-code
Content-Type: application/json

{
  "user_phone": "13800138000"
}
```

#### 步骤2: 用户注册
```
POST {{base_url}}/auth/register
Content-Type: application/json

{
  "user_phone": "13800138000",
  "user_verification_code": "123456"
}
```
**注意**: 验证码在控制台输出，默认为6位数字

#### 步骤3: 用户登录
```
POST {{base_url}}/auth/login
Content-Type: application/json

{
  "user_phone": "13800138000",
  "user_verification_code": "123456"
}
```
**保存返回的token到环境变量 `user_token`**

#### 步骤4: 更新用户信息
```
PUT {{base_url}}/user/profile
Authorization: Bearer {{user_token}}
Content-Type: application/json

{
  "user_name": "测试用户",
  "user_sex": 0
}
```

#### 步骤5: 添加宠物
```
POST {{base_url}}/pets
Authorization: Bearer {{user_token}}
Content-Type: application/json

{
  "pet_kind": "狗",
  "pet_age": "2022-01-01",
  "pet_weight": "5.5"
}
```
**注意**: 严格按照数据库表结构，只包含必要字段

#### 步骤6: 获取宠物列表
```
GET {{base_url}}/pets
Authorization: Bearer {{user_token}}
```

#### 步骤7: 获取服务列表
```
GET {{base_url}}/services?page=1&limit=10
```

#### 步骤8: 获取服务详情
```
GET {{base_url}}/services/{service_id}
```

#### 步骤9: 创建订单
```
POST {{base_url}}/orders
Authorization: Bearer {{user_token}}
Content-Type: application/json

{
  "service_id": "S00000001",
  "pet_id": "P0000001"
}
```

#### 步骤10: 模拟支付回调
```
POST {{base_url}}/orders/{order_id}/payment-callback
Content-Type: application/json

{
  "payment_id": "PAY00000001",
  "payment_status": 1,
  "payment_way": 0,
  "payment_price": 88.00,
  "trade_no": "alipay_trade_no"
}
```

#### 步骤11: 创建预约
```
POST {{base_url}}/appointments
Authorization: Bearer {{user_token}}
Content-Type: application/json

{
  "order_id": "O0000000001",
  "appointment_time": "2024-01-02T14:00:00Z",
  "appointment_location": "北京市朝阳区宠物店"
}
```

#### 步骤12: 获取订单列表
```
GET {{base_url}}/orders?page=1&limit=10
Authorization: Bearer {{user_token}}
```

### 2. 商家端测试流程

#### 步骤1: 商家登录
```
POST {{base_url}}/merchant/auth/login
Content-Type: application/json

{
  "merchant_account": "petshop001",
  "merchant_password": "123456"
}
```
**保存返回的token到环境变量 `merchant_token`**

#### 步骤2: 添加服务
```
POST {{base_url}}/merchant/services
Authorization: Bearer {{merchant_token}}
Content-Type: application/json

{
  "service_name": "宠物高级美容套餐",
  "service_kind": 0,
  "service_description": "包含洗澡、修剪毛发、清洁耳朵、指甲修剪等服务",
  "service_price": 128.00,
  "service_time": 180,
  "service_picture": ["url1", "url2"]
}
```

#### 步骤3: 获取商家服务列表
```
GET {{base_url}}/merchant/services?page=1&limit=10
Authorization: Bearer {{merchant_token}}
```

#### 步骤4: 获取商家订单列表
```
GET {{base_url}}/merchant/orders?page=1&limit=10
Authorization: Bearer {{merchant_token}}
```

#### 步骤5: 确认订单
```
PUT {{base_url}}/merchant/orders/{order_id}/confirm
Authorization: Bearer {{merchant_token}}
```

#### 步骤6: 完成订单
```
PUT {{base_url}}/merchant/orders/{order_id}/complete
Authorization: Bearer {{merchant_token}}
```

#### 步骤7: 获取预约列表
```
GET {{base_url}}/merchant/appointments?page=1&limit=10
Authorization: Bearer {{merchant_token}}
```

#### 步骤8: 确认预约
```
PUT {{base_url}}/merchant/appointments/{appointment_id}/confirm
Authorization: Bearer {{merchant_token}}
Content-Type: application/json

{
  "notes": "请准时到达，带好宠物用品"
}
```

#### 步骤9: 获取统计数据
```
GET {{base_url}}/merchant/statistics
Authorization: Bearer {{merchant_token}}
```

### 3. 通用接口测试

#### 文件上传
```
POST {{base_url}}/upload
Authorization: Bearer {{user_token}}
Content-Type: multipart/form-data

file: [选择图片文件]
type: service_image
```

#### 获取可用时间段
```
GET {{base_url}}/available-times?date=2024-01-02&service_id=S00000001&location=北京市朝阳区宠物店
```

## 测试数据

### 预置数据
应用启动时会自动创建以下测试数据：

**测试商家**:
- 账号: `petshop001`
- 密码: `123456`
- 商家名: `爱宠美容店`

**测试服务**:
- 服务名: `宠物基础美容套餐`
- 价格: `88.00`
- 类型: `美容(0)`

### 测试用户
可以使用任意11位手机号注册测试用户，验证码会在控制台输出。

## 常见问题

### 1. Token过期
如果接口返回401错误，说明Token已过期，需要重新登录获取新的Token。

### 2. 验证码获取
短信验证码会在控制台输出，格式为：`短信验证码: 手机号 -> 验证码`

### 3. 订单状态说明
- 0: 已取消
- 1: 已支付
- 2: 已预约
- 3: 已完成
- 4: 未支付

### 4. 预约状态说明
- 0: 待确认
- 1: 已确认
- 2: 已取消
- 3: 已完成

## 注意事项

1. 所有需要认证的接口都需要在请求头中添加 `Authorization: Bearer {token}`
2. 时间格式使用ISO 8601标准：`YYYY-MM-DDTHH:mm:ssZ`
3. 金额使用浮点数，保留两位小数
4. 文件上传仅支持图片格式（jpg, png, gif），大小限制5MB
5. 分页参数：page从1开始，limit默认为10

## 完整测试集合

建议在APIFOX中按以下顺序创建测试集合：

1. **用户认证** → 发送验证码 → 注册 → 登录 → 更新信息
2. **宠物管理** → 添加宠物 → 获取列表 → 删除宠物
3. **服务浏览** → 获取服务列表 → 获取服务详情
4. **订单流程** → 创建订单 → 支付回调 → 获取订单
5. **预约管理** → 创建预约 → 修改预约 → 取消预约
6. **评价系统** → 提交评价 → 获取评价列表
7. **商家认证** → 商家登录 → 更新信息
8. **商家服务** → 添加服务 → 获取列表 → 编辑服务
9. **商家订单** → 获取订单 → 确认订单 → 完成订单
10. **商家预约** → 获取预约 → 确认预约 → 修改预约
11. **数据统计** → 获取统计数据
12. **通用工具** → 文件上传 → 获取可用时间

这样可以完整测试整个宠物服务平台的所有功能。
