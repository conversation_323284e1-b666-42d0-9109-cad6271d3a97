#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的注册和登录接口
"""

import requests
import time
import re

BASE_URL = "http://localhost:5000/api"

def test_auth_fix():
    print("🔧 测试修复后的注册和登录接口...")
    print("=" * 50)
    
    # 测试1: 发送验证码
    print("📱 步骤1: 发送验证码")
    try:
        response = requests.post(f"{BASE_URL}/auth/send-code", 
                               json={"user_phone": "13800138000"})
        print(f"✅ 状态码: {response.status_code}")
        print(f"✅ 响应: {response.json()}")
        
        if response.status_code == 200:
            print("✅ 验证码发送成功！")
        else:
            print("❌ 验证码发送失败")
            return
    except Exception as e:
        print(f"❌ 错误: {e}")
        return
    
    print()
    
    # 获取Flask应用日志中的验证码
    print("📋 步骤2: 从Flask应用日志中获取验证码")
    print("请查看Flask应用控制台，找到类似这样的输出：")
    print("短信验证码: 13800138000 -> 123456")
    print()
    
    # 手动输入验证码进行测试
    verification_code = input("请输入从Flask控制台看到的验证码: ").strip()
    
    if not verification_code or len(verification_code) != 6:
        print("❌ 验证码格式错误，应该是6位数字")
        return
    
    print()
    
    # 测试2: 用户注册
    print("👤 步骤3: 用户注册")
    try:
        response = requests.post(f"{BASE_URL}/auth/register", 
                               json={
                                   "user_phone": "13800138000",
                                   "user_verification_code": verification_code
                               })
        print(f"✅ 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 响应格式: {data}")
            print("✅ 用户注册成功！")
            
            # 保存token用于后续测试
            user_token = data['data']['token']
            user_id = data['data']['user_id']
            print(f"✅ 用户ID: {user_id}")
            print(f"✅ Token: {user_token[:20]}...")
            
            # 测试3: 添加宠物（验证token是否有效）
            print()
            print("🐕 步骤4: 测试添加宠物（验证token）")
            headers = {"Authorization": f"Bearer {user_token}"}
            pet_data = {
                "pet_kind": "狗",
                "pet_age": "2022-01-01",
                "pet_weight": "5.5"
            }
            
            pet_response = requests.post(f"{BASE_URL}/pets", json=pet_data, headers=headers)
            print(f"✅ 添加宠物状态码: {pet_response.status_code}")
            
            if pet_response.status_code == 200:
                pet_data = pet_response.json()
                print(f"✅ 宠物添加成功: {pet_data}")
            else:
                print(f"❌ 宠物添加失败: {pet_response.text}")
            
        else:
            print(f"❌ 注册失败，状态码: {response.status_code}")
            print(f"❌ 响应: {response.text}")
    except Exception as e:
        print(f"❌ 注册错误: {e}")
    
    print()
    
    # 测试4: 用户登录（使用新的验证码）
    print("🔑 步骤5: 测试用户登录")
    try:
        # 先发送新的验证码
        print("发送新的验证码...")
        requests.post(f"{BASE_URL}/auth/send-code", 
                     json={"user_phone": "13800138000"})
        
        print("请查看Flask控制台获取新的验证码")
        new_code = input("请输入新的验证码: ").strip()
        
        if not new_code or len(new_code) != 6:
            print("❌ 验证码格式错误")
            return
        
        # 登录
        response = requests.post(f"{BASE_URL}/auth/login", 
                               json={
                                   "user_phone": "13800138000",
                                   "user_verification_code": new_code
                               })
        print(f"✅ 登录状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 登录成功: {data}")
        else:
            print(f"❌ 登录失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 登录错误: {e}")
    
    print()
    print("=" * 50)
    print("🎉 测试完成！")
    print()
    print("📋 总结:")
    print("1. HTTP状态码问题已修复")
    print("2. 请求参数使用正确的数据库字段名")
    print("3. 响应格式为 {code, data, message}")
    print("4. 现在可以在APIFOX中正常测试了")

if __name__ == "__main__":
    test_auth_fix()
