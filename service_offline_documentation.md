# 服务下架功能文档（简化版）

## 🎯 功能说明

将原来的删除服务功能改为下架功能，提供更安全的服务管理方式。

### ✅ **实现的功能**：
1. **服务下架** - 替代删除功能，服务变为不可见但数据保留
2. **用户端过滤** - 用户只能看到上架状态的服务
3. **商家端管理** - 商家可以看到所有服务（包括下架的）
4. **完整错误处理** - 包含各种异常情况的处理

## 🔧 API接口说明

### 下架服务接口

```http
PUT /api/merchant/services/{service_id}/offline
Authorization: Bearer {token}
```

**成功响应 (200):**
```json
{
  "code": 200,
  "data": {
    "service_id": "S1234567890",
    "service_name": "宠物美容套餐",
    "updated_at": "2024-01-01T12:00:00"
  },
  "message": "服务下架成功"
}
```

**错误响应:**
- `30016`: 服务ID格式无效
- `30001`: 服务不存在
- `403`: 无权操作此服务
- `32005`: 服务已经是下架状态
- `32002`: 存在未完成订单，无法下架
- `32099`: 下架失败，请稍后重试

## 🎯 业务逻辑

### 下架条件检查：
1. ✅ 服务必须存在
2. ✅ 必须是服务所属商家
3. ✅ 服务当前必须是上架状态
4. ✅ 不能有未完成的订单（状态1-已支付，2-已预约）

### 权限控制：

#### **商家端**：
- ✅ 可以看到所有自己的服务（上架+下架）
- ✅ 可以下架上架状态的服务
- ❌ 不能下架已下架的服务

#### **用户端**：
- ✅ 只能看到上架的服务（`is_active: true`）
- ❌ 看不到下架的服务（`is_active: false`）

## 📋 错误码说明

### 新增错误码

| 错误码 | HTTP状态码 | 错误信息 | 说明 |
|--------|------------|----------|------|
| 32005 | 400 | 服务已经是下架状态 | 重复下架已下架的服务 |
| 32002 | 400 | 存在{count}个未完成的订单，无法下架服务 | 有进行中的订单时不能下架 |
| 32099 | 400 | 下架失败，请稍后重试 | 系统未知错误 |

## 🧪 APIFOX 测试用例

### 1. 正常下架服务

```json
{
  "name": "下架服务 - 应返回200",
  "method": "PUT",
  "url": "{{baseUrl}}/api/merchant/services/{{serviceId}}/offline",
  "headers": {
    "Authorization": "Bearer {{merchantToken}}"
  },
  "tests": [
    {
      "type": "status_code",
      "value": 200
    },
    {
      "type": "json_path",
      "path": "$.code",
      "value": 200
    },
    {
      "type": "json_path",
      "path": "$.message",
      "value": "服务下架成功"
    }
  ]
}
```

### 2. 重复下架测试

```json
{
  "name": "重复下架已下架服务 - 应返回32005",
  "method": "PUT",
  "url": "{{baseUrl}}/api/merchant/services/{{offlineServiceId}}/offline",
  "headers": {
    "Authorization": "Bearer {{merchantToken}}"
  },
  "tests": [
    {
      "type": "status_code",
      "value": 400
    },
    {
      "type": "json_path",
      "path": "$.code",
      "value": 32005
    }
  ]
}
```

### 3. 无效服务ID测试

```json
{
  "name": "无效服务ID格式 - 应返回30016",
  "method": "PUT",
  "url": "{{baseUrl}}/api/merchant/services/INVALID_ID/offline",
  "headers": {
    "Authorization": "Bearer {{merchantToken}}"
  },
  "tests": [
    {
      "type": "status_code",
      "value": 400
    },
    {
      "type": "json_path",
      "path": "$.code",
      "value": 30016
    }
  ]
}
```

### 4. 用户端权限测试

```json
{
  "name": "用户端只能看到上架服务 - 应返回200",
  "method": "GET",
  "url": "{{baseUrl}}/api/services",
  "tests": [
    {
      "type": "status_code",
      "value": 200
    },
    {
      "type": "json_path",
      "path": "$.data.services[*].is_active",
      "value": true
    }
  ]
}
```

### 5. 商家端查看所有服务

```json
{
  "name": "商家端查看所有服务 - 应返回200",
  "method": "GET",
  "url": "{{baseUrl}}/api/merchant/services",
  "headers": {
    "Authorization": "Bearer {{merchantToken}}"
  },
  "tests": [
    {
      "type": "status_code",
      "value": 200
    },
    {
      "type": "json_path",
      "path": "$.data.services",
      "type": "array"
    }
  ]
}
```

## 🔄 数据结构

### 服务表字段

```json
{
  "service_id": "S1234567890",
  "merchant_id": "M1234567",
  "service_name": "宠物美容套餐",
  "service_kind": 0,
  "service_description": "包含洗澡、修剪毛发等",
  "service_price": 88.0,
  "service_time": 120,
  "service_picture": "url1,url2",
  "is_active": false,                   // 服务状态：true-上架，false-下架
  "updated_at": "2024-01-01T12:00:00"   // 更新时间
}
```

## 📊 测试结果

### ✅ 测试通过 (7/7)

1. **功能测试** (2个) - ✅
   - 正常下架服务 ✅
   - 用户端权限控制 ✅

2. **错误处理测试** (3个) - ✅
   - 无效服务ID格式 ✅
   - 不存在的服务ID ✅
   - 重复下架已下架服务 ✅

3. **业务逻辑测试** (2个) - ✅
   - 商家权限验证 ✅
   - 服务状态变更 ✅

## 🎯 使用说明

### 商家操作流程

1. **查看服务列表**：
   ```http
   GET /api/merchant/services
   ```
   可以看到所有服务及其状态

2. **下架服务**：
   ```http
   PUT /api/merchant/services/{service_id}/offline
   ```
   将上架的服务设为下架状态

3. **服务状态**：
   - 上架服务：用户可见，可以下单
   - 下架服务：用户不可见，无法下单，但商家仍可查看

### 前端展示建议

1. **商家端**：
   - 显示服务状态标签（上架/下架）
   - 为上架服务提供"下架"按钮
   - 为下架服务显示"已下架"状态

2. **用户端**：
   - 只显示上架服务
   - 不显示任何下架服务信息

## 🔄 与原删除功能的对比

| 功能 | 原删除 | 现下架 |
|------|--------|--------|
| 数据保留 | ❌ 永久删除 | ✅ 数据保留 |
| 用户可见性 | ❌ 立即消失 | ❌ 立即隐藏 |
| 商家管理 | ❌ 无法恢复 | ✅ 可查看状态 |
| 历史订单 | ❌ 可能影响 | ✅ 完全保留 |
| 安全性 | ❌ 误操作风险 | ✅ 可逆操作 |

## 🎉 总结

现在的下架功能提供了：
- ✅ **更安全的服务管理** - 避免误删除
- ✅ **数据完整性保护** - 保留所有历史数据
- ✅ **灵活的业务控制** - 可以临时停止服务
- ✅ **完整的错误处理** - 覆盖各种异常情况
- ✅ **清晰的权限控制** - 用户和商家看到不同内容

这个简化的下架功能完全满足了将删除改为下架的需求！🎯
