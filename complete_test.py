#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整测试确认预约接口和商家详情接口
"""

import requests
import json
import time

BASE_URL = "http://localhost:5000/api"

def test_complete_flow():
    """测试完整流程"""
    print("=== 完整流程测试 ===")
    
    # 1. 商家登录
    print("\n1. 商家登录...")
    login_data = {
        "merchant_account": "petshop001",
        "merchant_password": "123456"
    }
    
    response = requests.post(f"{BASE_URL}/merchant/auth/login", json=login_data)
    if response.status_code != 200:
        print("商家登录失败")
        return
    
    login_result = response.json()
    merchant_token = login_result['data']['token']
    merchant_id = login_result['data']['merchant_id']
    print(f"✅ 商家登录成功，ID: {merchant_id}")
    
    # 2. 用户注册
    print("\n2. 用户注册...")
    
    # 发送验证码
    send_code_data = {"user_phone": "***********"}
    response = requests.post(f"{BASE_URL}/auth/send-code", json=send_code_data)
    print(f"发送验证码响应: {response.status_code}")
    
    # 等待验证码生成
    time.sleep(1)
    
    # 注册用户（使用固定验证码进行测试）
    # 注意：在实际测试中，您需要从控制台输出中获取真实的验证码
    user_register_data = {
        "user_phone": "***********",
        "user_verification_code": "875978"  # 使用真实的验证码
    }
    
    response = requests.post(f"{BASE_URL}/auth/register", json=user_register_data)
    if response.status_code != 200:
        # 如果注册失败，尝试登录
        response = requests.post(f"{BASE_URL}/auth/login", json=user_register_data)
    
    if response.status_code != 200:
        print("用户注册/登录失败，跳过用户相关测试")
        print("但我们可以继续测试商家接口...")
        test_merchant_apis_only(merchant_token)
        return
    
    user_result = response.json()
    user_token = user_result['data']['token']
    user_id = user_result['data']['user_id']
    print(f"✅ 用户注册/登录成功，ID: {user_id}")
    
    # 3. 添加宠物
    print("\n3. 添加宠物...")
    pet_data = {
        "pet_kind": "狗",
        "pet_age": "2023-01-01",
        "pet_weight": "5.5"
    }
    
    user_headers = {"Authorization": f"Bearer {user_token}"}
    response = requests.post(f"{BASE_URL}/pets", json=pet_data, headers=user_headers)
    
    if response.status_code != 200:
        print("添加宠物失败")
        return
    
    pet_result = response.json()
    pet_id = pet_result['data']['pet_id']
    print(f"✅ 添加宠物成功，ID: {pet_id}")
    
    # 4. 获取服务列表
    print("\n4. 获取服务列表...")
    response = requests.get(f"{BASE_URL}/services")
    
    if response.status_code != 200:
        print("获取服务列表失败")
        return
    
    services_result = response.json()
    if not services_result['data']['services']:
        print("没有可用的服务")
        return
    
    service_id = services_result['data']['services'][0]['service_id']
    print(f"✅ 获取服务成功，使用服务ID: {service_id}")
    
    # 5. 创建订单
    print("\n5. 创建订单...")
    order_data = {
        "service_id": service_id,
        "pet_id": pet_id
    }
    
    response = requests.post(f"{BASE_URL}/orders", json=order_data, headers=user_headers)
    
    if response.status_code != 200:
        print("创建订单失败")
        return
    
    order_result = response.json()
    order_id = order_result['data']['order_id']
    print(f"✅ 创建订单成功，ID: {order_id}")
    
    # 6. 模拟支付
    print("\n6. 模拟支付...")
    payment_data = {"payment_status": 1}
    response = requests.post(f"{BASE_URL}/orders/{order_id}/payment-callback", json=payment_data)
    
    if response.status_code != 200:
        print("支付失败")
        return
    
    print("✅ 支付成功")
    
    # 7. 创建预约
    print("\n7. 创建预约...")
    appointment_data = {
        "order_id": order_id,
        "appointment_time": "2024-12-25 10:00:00",
        "appointment_location": "北京市朝阳区xxx街道"
    }
    
    response = requests.post(f"{BASE_URL}/appointments", json=appointment_data, headers=user_headers)
    
    if response.status_code != 200:
        print("创建预约失败")
        return
    
    appointment_result = response.json()
    appointment_id = appointment_result['data']['appointment_id']
    print(f"✅ 创建预约成功，ID: {appointment_id}")
    
    # 8. 测试商家接口
    print("\n8. 测试商家接口...")
    merchant_headers = {"Authorization": f"Bearer {merchant_token}"}
    
    # 8.1 获取商家订单详情
    print("\n8.1 测试获取商家订单详情...")
    response = requests.get(f"{BASE_URL}/merchant/orders/{order_id}", headers=merchant_headers)
    print(f"获取订单详情响应: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 获取商家订单详情成功")
        print(f"订单信息: {json.dumps(result['data'], indent=2, ensure_ascii=False)}")
    else:
        print(f"❌ 获取订单详情失败: {response.text}")
    
    # 8.2 获取商家预约详情
    print("\n8.2 测试获取商家预约详情...")
    response = requests.get(f"{BASE_URL}/merchant/appointments/{appointment_id}", headers=merchant_headers)
    print(f"获取预约详情响应: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 获取商家预约详情成功")
        print(f"预约信息: {json.dumps(result['data'], indent=2, ensure_ascii=False)}")
    else:
        print(f"❌ 获取预约详情失败: {response.text}")
    
    # 8.3 确认预约
    print("\n8.3 测试确认预约...")
    confirm_data = {
        "notes": "预约确认，请准时到达"
    }
    
    response = requests.put(f"{BASE_URL}/merchant/appointments/{appointment_id}/confirm", 
                          json=confirm_data, headers=merchant_headers)
    
    print(f"确认预约响应状态码: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 确认预约成功！")
        print(f"确认结果: {json.dumps(result['data'], indent=2, ensure_ascii=False)}")
    else:
        result = response.json()
        print(f"❌ 确认预约失败: {result}")
    
    print("\n=== 测试完成 ===")

def test_merchant_apis_only(merchant_token):
    """仅测试商家接口"""
    print("\n=== 仅测试商家接口 ===")
    
    merchant_headers = {"Authorization": f"Bearer {merchant_token}"}
    
    # 测试获取订单列表
    response = requests.get(f"{BASE_URL}/merchant/orders", headers=merchant_headers)
    print(f"✅ 获取商家订单列表: {response.status_code}")
    
    # 测试获取预约列表
    response = requests.get(f"{BASE_URL}/merchant/appointments", headers=merchant_headers)
    print(f"✅ 获取商家预约列表: {response.status_code}")
    
    print("✅ 商家接口测试完成")

if __name__ == "__main__":
    test_complete_flow()
