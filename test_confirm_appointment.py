#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试确认预约接口的脚本
"""

import requests
import json

BASE_URL = "http://localhost:5000/api"

def test_confirm_appointment():
    """测试确认预约接口"""
    print("=== 测试确认预约接口 ===")
    
    # 1. 商家登录
    print("\n1. 商家登录...")

    # 商家登录（使用账号密码）
    login_data = {
        "merchant_account": "petshop001",
        "merchant_password": "123456"
    }
    
    response = requests.post(f"{BASE_URL}/merchant/auth/login", json=login_data)
    print(f"商家登录响应: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    if response.status_code != 200:
        print("商家登录失败")
        return
    
    login_result = response.json()
    merchant_token = login_result['data']['token']
    print(f"商家Token: {merchant_token}")
    
    # 2. 创建一个测试预约
    print("\n2. 创建测试数据...")
    
    # 先注册用户
    user_send_code_data = {
        "user_phone": "***********"
    }
    
    response = requests.post(f"{BASE_URL}/auth/send-code", json=user_send_code_data)
    print(f"用户发送验证码响应: {response.status_code}")
    print(f"验证码响应内容: {response.text}")

    # 从控制台输出中获取验证码（实际项目中不需要这样做）
    import time
    time.sleep(1)  # 等待验证码生成

    user_register_data = {
        "user_phone": "***********",
        "user_verification_code": "066335"  # 使用实际的验证码
    }

    response = requests.post(f"{BASE_URL}/auth/register", json=user_register_data)
    print(f"用户注册响应: {response.status_code}")
    print(f"注册响应内容: {response.text}")

    if response.status_code != 200:
        print("用户注册失败，尝试登录...")
        # 如果注册失败，可能用户已存在，尝试登录
        response = requests.post(f"{BASE_URL}/auth/login", json=user_register_data)
        print(f"用户登录响应: {response.status_code}")
        print(f"登录响应内容: {response.text}")

        if response.status_code != 200:
            print("用户登录也失败")
            return
    
    user_result = response.json()
    user_token = user_result['data']['token']
    user_id = user_result['data']['user_id']
    print(f"用户Token: {user_token}")
    
    # 添加宠物
    pet_data = {
        "pet_kind": "狗",
        "pet_age": "2023-01-01",
        "pet_weight": "5.5"
    }
    
    headers = {"Authorization": f"Bearer {user_token}"}
    response = requests.post(f"{BASE_URL}/pets", json=pet_data, headers=headers)
    print(f"添加宠物响应: {response.status_code}")
    
    if response.status_code != 200:
        print("添加宠物失败")
        return
    
    pet_result = response.json()
    pet_id = pet_result['data']['pet_id']
    print(f"宠物ID: {pet_id}")
    
    # 获取服务列表
    response = requests.get(f"{BASE_URL}/services")
    print(f"获取服务列表响应: {response.status_code}")

    if response.status_code != 200:
        print("获取服务列表失败")
        return

    services_result = response.json()
    if not services_result['data']['services']:
        print("没有可用的服务")
        return

    service_id = services_result['data']['services'][0]['service_id']
    print(f"使用服务ID: {service_id}")

    # 创建订单
    order_data = {
        "service_id": service_id,
        "pet_id": pet_id
    }
    
    response = requests.post(f"{BASE_URL}/orders", json=order_data, headers=headers)
    print(f"创建订单响应: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    if response.status_code != 200:
        print("创建订单失败")
        return
    
    order_result = response.json()
    order_id = order_result['data']['order_id']
    print(f"订单ID: {order_id}")
    
    # 模拟支付
    payment_data = {
        "payment_status": 1
    }
    
    response = requests.post(f"{BASE_URL}/orders/{order_id}/payment-callback", json=payment_data)
    print(f"支付回调响应: {response.status_code}")
    
    # 创建预约
    appointment_data = {
        "order_id": order_id,
        "appointment_time": "2024-12-25 10:00:00",
        "appointment_location": "北京市朝阳区xxx街道"
    }
    
    response = requests.post(f"{BASE_URL}/appointments", json=appointment_data, headers=headers)
    print(f"创建预约响应: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    if response.status_code != 200:
        print("创建预约失败")
        return
    
    appointment_result = response.json()
    appointment_id = appointment_result['data']['appointment_id']
    print(f"预约ID: {appointment_id}")
    
    # 3. 测试确认预约接口
    print("\n3. 测试确认预约接口...")
    
    confirm_data = {
        "notes": "预约确认，请准时到达"
    }
    
    merchant_headers = {"Authorization": f"Bearer {merchant_token}"}
    response = requests.put(f"{BASE_URL}/merchant/appointments/{appointment_id}/confirm", 
                          json=confirm_data, headers=merchant_headers)
    
    print(f"确认预约响应状态码: {response.status_code}")
    print(f"响应头: {dict(response.headers)}")
    print(f"响应内容: {response.text}")
    
    # 检查响应是否为JSON格式
    try:
        result = response.json()
        print(f"JSON解析成功: {result}")
        
        if response.status_code == 200:
            print("✅ 确认预约接口测试成功！")
        else:
            print(f"❌ 确认预约接口返回错误: {result}")
    except json.JSONDecodeError as e:
        print(f"❌ 响应不是有效的JSON格式: {e}")
        print(f"原始响应: {response.text}")
    
    # 4. 测试获取商家订单详情
    print("\n4. 测试获取商家订单详情...")
    
    response = requests.get(f"{BASE_URL}/merchant/orders/{order_id}", headers=merchant_headers)
    print(f"获取订单详情响应状态码: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    # 5. 测试获取商家预约详情
    print("\n5. 测试获取商家预约详情...")
    
    response = requests.get(f"{BASE_URL}/merchant/appointments/{appointment_id}", headers=merchant_headers)
    print(f"获取预约详情响应状态码: {response.status_code}")
    print(f"响应内容: {response.text}")

if __name__ == "__main__":
    test_confirm_appointment()
