#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多用户注册和商家注册功能
"""

import requests
import time

BASE_URL = "http://localhost:5000/api"

def test_multiple_users():
    print("👥 测试多用户注册和商家注册功能...")
    print("=" * 60)
    
    # 测试1: 注册第一个用户
    print("👤 测试1: 注册第一个用户 (13800138001)")
    try:
        # 发送验证码
        response = requests.post(f"{BASE_URL}/auth/send-code", 
                               json={"user_phone": "13800138001"})
        if response.status_code == 200:
            print("✅ 验证码发送成功")
            
            # 注册用户（使用固定验证码进行演示）
            response = requests.post(f"{BASE_URL}/auth/register", 
                                   json={"user_phone": "13800138001", "user_verification_code": "123456"})
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 第一个用户注册成功: {data['data']['user_id']}")
                user1_token = data['data']['token']
            else:
                print(f"❌ 第一个用户注册失败: {response.json()}")
        else:
            print(f"❌ 验证码发送失败: {response.json()}")
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    print()
    
    # 测试2: 注册第二个用户（不同手机号）
    print("👤 测试2: 注册第二个用户 (13800138002)")
    try:
        # 发送验证码
        response = requests.post(f"{BASE_URL}/auth/send-code", 
                               json={"user_phone": "13800138002"})
        if response.status_code == 200:
            print("✅ 验证码发送成功")
            
            # 注册用户
            response = requests.post(f"{BASE_URL}/auth/register", 
                                   json={"user_phone": "13800138002", "user_verification_code": "123456"})
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 第二个用户注册成功: {data['data']['user_id']}")
                user2_token = data['data']['token']
            else:
                print(f"❌ 第二个用户注册失败: {response.json()}")
        else:
            print(f"❌ 验证码发送失败: {response.json()}")
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    print()
    
    # 测试3: 尝试用相同手机号再次注册（应该失败）
    print("👤 测试3: 尝试重复注册相同手机号 (13800138001)")
    try:
        # 发送验证码
        response = requests.post(f"{BASE_URL}/auth/send-code", 
                               json={"user_phone": "13800138001"})
        if response.status_code == 200:
            # 尝试注册
            response = requests.post(f"{BASE_URL}/auth/register", 
                                   json={"user_phone": "13800138001", "user_verification_code": "123456"})
            if response.status_code != 200:
                data = response.json()
                print(f"✅ 正确阻止重复注册: {data['message']}")
            else:
                print("❌ 应该阻止重复注册但没有阻止")
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    print()
    
    # 测试4: 注册第一个商家
    print("🏪 测试4: 注册第一个商家 (petshop002)")
    try:
        response = requests.post(f"{BASE_URL}/merchant/auth/register", 
                               json={
                                   "merchant_account": "petshop002",
                                   "merchant_password": "123456",
                                   "merchant_name": "宠物乐园",
                                   "merchant_phone": "***********",
                                   "merchant_location": "上海市浦东新区"
                               })
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 第一个商家注册成功: {data['data']['merchant_id']}")
            print(f"✅ 商家名: {data['data']['merchant_info']['merchant_name']}")
            merchant1_token = data['data']['token']
        else:
            print(f"❌ 第一个商家注册失败: {response.json()}")
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    print()
    
    # 测试5: 注册第二个商家
    print("🏪 测试5: 注册第二个商家 (petshop003)")
    try:
        response = requests.post(f"{BASE_URL}/merchant/auth/register", 
                               json={
                                   "merchant_account": "petshop003",
                                   "merchant_password": "123456",
                                   "merchant_name": "宠物天堂",
                                   "merchant_phone": "***********",
                                   "merchant_location": "广州市天河区"
                               })
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 第二个商家注册成功: {data['data']['merchant_id']}")
            print(f"✅ 商家名: {data['data']['merchant_info']['merchant_name']}")
            merchant2_token = data['data']['token']
        else:
            print(f"❌ 第二个商家注册失败: {response.json()}")
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    print()
    
    # 测试6: 尝试注册重复的商家账号（应该失败）
    print("🏪 测试6: 尝试注册重复商家账号 (petshop002)")
    try:
        response = requests.post(f"{BASE_URL}/merchant/auth/register", 
                               json={
                                   "merchant_account": "petshop002",  # 重复账号
                                   "merchant_password": "123456",
                                   "merchant_name": "重复商家",
                                   "merchant_phone": "***********"
                               })
        if response.status_code != 200:
            data = response.json()
            print(f"✅ 正确阻止重复商家注册: {data['message']}")
        else:
            print("❌ 应该阻止重复商家注册但没有阻止")
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    print()
    
    # 测试7: 测试预置商家登录
    print("🏪 测试7: 测试预置商家登录 (petshop001)")
    try:
        response = requests.post(f"{BASE_URL}/merchant/auth/login", 
                               json={
                                   "merchant_account": "petshop001",
                                   "merchant_password": "123456"
                               })
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 预置商家登录成功: {data['data']['merchant_info']['merchant_name']}")
        else:
            print(f"❌ 预置商家登录失败: {response.json()}")
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    print()
    print("=" * 60)
    print("🎯 测试总结:")
    print("1. ✅ 可以注册多个不同手机号的用户")
    print("2. ✅ 正确阻止相同手机号重复注册")
    print("3. ✅ 可以注册多个不同账号的商家")
    print("4. ✅ 正确阻止相同账号重复注册商家")
    print("5. ✅ 预置商家账号正常工作")
    print()
    print("📋 APIFOX测试建议:")
    print("- 用户注册: 使用不同的手机号进行测试")
    print("- 商家注册: 使用不同的merchant_account进行测试")
    print("- 预置商家: petshop001 / 123456")
    print("- 新注册商家: petshop002 / 123456, petshop003 / 123456")

if __name__ == "__main__":
    test_multiple_users()
