# 数据库设计建议和优化

## 原有表结构分析

基于您提供的8个表设计，我发现了一些可以优化的地方，以下是建议：

## 表结构优化建议

### 1. 添加缺失的字段

#### users 表优化
```sql
CREATE TABLE users (
    user_id VARCHAR(8) PRIMARY KEY,
    user_phone CHAR(11) NOT NULL UNIQUE,
    user_verification_code CHAR(6),
    user_name VARCHAR(20),
    user_sex ENUM('0', '1', '2') DEFAULT '2', -- 0-男，1-女，2-保密
    avatar_url VARCHAR(255), -- 用户头像
    user_level INT DEFAULT 1, -- 用户等级
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE -- 账号状态
);
```

#### pets 表优化
```sql
CREATE TABLE pets (
    pet_id VARCHAR(8) PRIMARY KEY,
    user_id VARCHAR(8) NOT NULL,
    pet_name VARCHAR(50), -- 宠物名称
    pet_kind VARCHAR(20) NOT NULL, -- 宠物种类
    pet_breed VARCHAR(50), -- 宠物品种
    pet_weight DECIMAL(5,2), -- 宠物体重，支持小数
    pet_age DATE NOT NULL, -- 宠物出生日期
    pet_gender ENUM('0', '1', '2') DEFAULT '2', -- 0-公，1-母，2-未知
    pet_avatar VARCHAR(255), -- 宠物头像
    health_status TEXT, -- 健康状况
    special_notes TEXT, -- 特殊说明
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);
```

#### service 表优化
```sql
CREATE TABLE service (
    service_id VARCHAR(11) PRIMARY KEY,
    merchant_id VARCHAR(8) NOT NULL, -- 关联商家
    service_name VARCHAR(100) NOT NULL,
    service_kind ENUM('0', '1', '2', '3') NOT NULL, -- 0-美容，1-寄养，2-清洁，3-训练
    service_description TEXT,
    service_price DECIMAL(10,2) NOT NULL,
    service_time INT NOT NULL, -- 服务时长（分钟）
    service_picture JSON, -- 存储图片URL数组
    -- 宠物要求
    min_weight DECIMAL(5,2), -- 最小体重要求
    max_weight DECIMAL(5,2), -- 最大体重要求
    min_age_months INT, -- 最小年龄要求（月）
    max_age_months INT, -- 最大年龄要求（月）
    allowed_pet_kinds JSON, -- 允许的宠物种类
    -- 服务状态
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0, -- 排序权重
    view_count INT DEFAULT 0, -- 浏览次数
    order_count INT DEFAULT 0, -- 订单数量
    average_rating DECIMAL(3,2) DEFAULT 0, -- 平均评分
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (merchant_id) REFERENCES merchant(merchant_id)
);
```

#### order 表优化
```sql
CREATE TABLE `order` (
    order_id VARCHAR(11) PRIMARY KEY,
    user_id VARCHAR(8) NOT NULL,
    merchant_id VARCHAR(8) NOT NULL, -- 添加商家ID
    service_id VARCHAR(11) NOT NULL,
    pet_id VARCHAR(8) NOT NULL,
    order_status ENUM('0', '1', '2', '3', '4') NOT NULL, -- 0-已取消，1-已支付，2-已预约，3-已完成，4-未支付
    order_price DECIMAL(10,2) NOT NULL,
    original_price DECIMAL(10,2), -- 原价
    discount_amount DECIMAL(10,2) DEFAULT 0, -- 优惠金额
    cancel_reason TEXT, -- 取消原因
    refund_amount DECIMAL(10,2) DEFAULT 0, -- 退款金额
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expired_at TIMESTAMP, -- 订单过期时间
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (merchant_id) REFERENCES merchant(merchant_id),
    FOREIGN KEY (service_id) REFERENCES service(service_id),
    FOREIGN KEY (pet_id) REFERENCES pets(pet_id)
);
```

#### appointment 表优化
```sql
CREATE TABLE appointment (
    appointment_id VARCHAR(11) PRIMARY KEY,
    order_id VARCHAR(11) NOT NULL UNIQUE,
    user_id VARCHAR(8) NOT NULL,
    merchant_id VARCHAR(8) NOT NULL,
    appointment_time DATETIME NOT NULL,
    appointment_location TEXT NOT NULL,
    appointment_status ENUM('0', '1', '2', '3') DEFAULT '0', -- 0-待确认，1-已确认，2-已取消，3-已完成
    merchant_notes TEXT, -- 商家备注
    user_notes TEXT, -- 用户备注
    cancel_reason TEXT, -- 取消原因
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES `order`(order_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (merchant_id) REFERENCES merchant(merchant_id)
);
```

#### payment 表优化
```sql
CREATE TABLE payment (
    payment_id VARCHAR(11) PRIMARY KEY,
    order_id VARCHAR(11) NOT NULL,
    payment_way ENUM('0', '1', '2') NOT NULL, -- 0-支付宝，1-微信，2-其他
    payment_price DECIMAL(10,2) NOT NULL,
    payment_status ENUM('0', '1', '2', '3') NOT NULL, -- 0-待支付，1-支付成功，2-支付失败，3-已退款
    trade_no VARCHAR(100), -- 第三方交易号
    refund_no VARCHAR(100), -- 退款单号
    payment_time TIMESTAMP NULL,
    refund_time TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES `order`(order_id)
);
```

#### evaluation 表优化
```sql
CREATE TABLE evaluation (
    evaluation_id VARCHAR(11) PRIMARY KEY,
    user_id VARCHAR(8) NOT NULL,
    order_id VARCHAR(11) NOT NULL UNIQUE,
    merchant_id VARCHAR(8) NOT NULL,
    service_id VARCHAR(11) NOT NULL,
    evaluation_star TINYINT UNSIGNED NOT NULL CHECK (evaluation_star BETWEEN 1 AND 5),
    evaluation_context TEXT,
    evaluation_picture JSON, -- 存储图片URL数组
    merchant_reply TEXT, -- 商家回复
    reply_time TIMESTAMP NULL, -- 回复时间
    is_anonymous BOOLEAN DEFAULT FALSE, -- 是否匿名评价
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (order_id) REFERENCES `order`(order_id),
    FOREIGN KEY (merchant_id) REFERENCES merchant(merchant_id),
    FOREIGN KEY (service_id) REFERENCES service(service_id)
);
```

#### merchant 表优化
```sql
CREATE TABLE merchant (
    merchant_id VARCHAR(8) PRIMARY KEY,
    merchant_account VARCHAR(50) NOT NULL UNIQUE,
    merchant_password VARCHAR(255) NOT NULL, -- 建议加密存储
    merchant_name VARCHAR(200) NOT NULL,
    merchant_phone CHAR(11),
    merchant_location TEXT,
    merchant_description TEXT, -- 商家描述
    business_license VARCHAR(100), -- 营业执照号
    contact_person VARCHAR(50), -- 联系人
    business_hours JSON, -- 营业时间
    service_areas JSON, -- 服务区域
    logo_url VARCHAR(255), -- 商家logo
    status ENUM('0', '1', '2') DEFAULT '1', -- 0-禁用，1-正常，2-审核中
    average_rating DECIMAL(3,2) DEFAULT 0, -- 平均评分
    total_orders INT DEFAULT 0, -- 总订单数
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2. 新增建议表

#### 系统配置表
```sql
CREATE TABLE system_config (
    config_id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT,
    config_description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 短信验证码记录表
```sql
CREATE TABLE sms_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    phone CHAR(11) NOT NULL,
    code CHAR(6) NOT NULL,
    type ENUM('register', 'login', 'reset') NOT NULL,
    status ENUM('sent', 'used', 'expired') DEFAULT 'sent',
    ip_address VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expired_at TIMESTAMP NOT NULL,
    INDEX idx_phone_type (phone, type),
    INDEX idx_created_at (created_at)
);
```

#### 操作日志表
```sql
CREATE TABLE operation_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_type ENUM('user', 'merchant') NOT NULL,
    user_id VARCHAR(8) NOT NULL,
    operation VARCHAR(100) NOT NULL,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user (user_type, user_id),
    INDEX idx_created_at (created_at)
);
```

## 索引优化建议

```sql
-- users 表索引
CREATE INDEX idx_users_phone ON users(user_phone);
CREATE INDEX idx_users_created_at ON users(created_at);

-- pets 表索引
CREATE INDEX idx_pets_user_id ON pets(user_id);
CREATE INDEX idx_pets_kind ON pets(pet_kind);

-- service 表索引
CREATE INDEX idx_service_merchant_id ON service(merchant_id);
CREATE INDEX idx_service_kind ON service(service_kind);
CREATE INDEX idx_service_price ON service(service_price);
CREATE INDEX idx_service_rating ON service(average_rating);

-- order 表索引
CREATE INDEX idx_order_user_id ON `order`(user_id);
CREATE INDEX idx_order_merchant_id ON `order`(merchant_id);
CREATE INDEX idx_order_status ON `order`(order_status);
CREATE INDEX idx_order_created_at ON `order`(created_at);

-- appointment 表索引
CREATE INDEX idx_appointment_time ON appointment(appointment_time);
CREATE INDEX idx_appointment_merchant_id ON appointment(merchant_id);
CREATE INDEX idx_appointment_status ON appointment(appointment_status);

-- payment 表索引
CREATE INDEX idx_payment_order_id ON payment(order_id);
CREATE INDEX idx_payment_status ON payment(payment_status);
CREATE INDEX idx_payment_trade_no ON payment(trade_no);

-- evaluation 表索引
CREATE INDEX idx_evaluation_service_id ON evaluation(service_id);
CREATE INDEX idx_evaluation_merchant_id ON evaluation(merchant_id);
CREATE INDEX idx_evaluation_star ON evaluation(evaluation_star);
```

## 数据完整性约束

1. **外键约束**：确保数据引用完整性
2. **唯一约束**：防止重复数据
3. **检查约束**：确保数据有效性（如评分范围1-5）
4. **非空约束**：确保关键字段不为空

## 性能优化建议

1. **分区表**：对于大数据量的订单表，可考虑按时间分区
2. **读写分离**：配置主从数据库，读写分离
3. **缓存策略**：热点数据使用Redis缓存
4. **定期清理**：定期清理过期的验证码记录和日志数据
