#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试确认预约接口和商家详情接口
"""

import requests
import json

BASE_URL = "http://localhost:5000/api"

# 使用启动时显示的测试数据
TEST_ORDER_ID = "O7095123121"
TEST_APPOINTMENT_ID = "A2588745645"

def test_final():
    """最终测试"""
    print("=== 最终测试：确认预约接口和商家详情接口 ===")
    
    # 1. 商家登录
    print("\n1. 商家登录...")
    login_data = {
        "merchant_account": "petshop001",
        "merchant_password": "123456"
    }
    
    response = requests.post(f"{BASE_URL}/merchant/auth/login", json=login_data)
    if response.status_code != 200:
        print("❌ 商家登录失败")
        return
    
    login_result = response.json()
    merchant_token = login_result['data']['token']
    merchant_id = login_result['data']['merchant_id']
    print(f"✅ 商家登录成功，ID: {merchant_id}")
    
    merchant_headers = {"Authorization": f"Bearer {merchant_token}"}
    
    # 2. 测试获取商家订单详情
    print(f"\n2. 测试获取商家订单详情 (订单ID: {TEST_ORDER_ID})...")
    
    response = requests.get(f"{BASE_URL}/merchant/orders/{TEST_ORDER_ID}", headers=merchant_headers)
    print(f"响应状态码: {response.status_code}")
    print(f"响应头 Content-Type: {response.headers.get('Content-Type')}")
    
    try:
        result = response.json()
        if response.status_code == 200:
            print("✅ 获取商家订单详情成功！")
            print("订单详情包含以下信息:")
            data = result['data']
            print(f"  - 订单ID: {data['order_id']}")
            print(f"  - 订单状态: {data['order_status']}")
            print(f"  - 订单价格: {data['order_price']}")
            if 'user_info' in data:
                print(f"  - 用户信息: {data['user_info']}")
            if 'service_info' in data:
                print(f"  - 服务信息: {data['service_info']['service_name']}")
            if 'pet_info' in data:
                print(f"  - 宠物信息: {data['pet_info']}")
            if 'appointment_info' in data:
                print(f"  - 预约信息: {data['appointment_info']}")
        else:
            print(f"❌ 获取订单详情失败: {result}")
    except json.JSONDecodeError as e:
        print(f"❌ 响应不是有效的JSON格式: {e}")
        print(f"原始响应: {response.text}")
    
    # 3. 测试获取商家预约详情
    print(f"\n3. 测试获取商家预约详情 (预约ID: {TEST_APPOINTMENT_ID})...")
    
    response = requests.get(f"{BASE_URL}/merchant/appointments/{TEST_APPOINTMENT_ID}", headers=merchant_headers)
    print(f"响应状态码: {response.status_code}")
    print(f"响应头 Content-Type: {response.headers.get('Content-Type')}")
    
    try:
        result = response.json()
        if response.status_code == 200:
            print("✅ 获取商家预约详情成功！")
            print("预约详情包含以下信息:")
            data = result['data']
            print(f"  - 预约ID: {data['appointment_id']}")
            print(f"  - 预约时间: {data['appointment_time']}")
            print(f"  - 预约地点: {data['appointment_location']}")
            print(f"  - 预约状态: {data['appointment_status']}")
            if 'order_info' in data:
                print(f"  - 订单信息: {data['order_info']}")
            if 'user_info' in data:
                print(f"  - 用户信息: {data['user_info']}")
            if 'service_info' in data:
                print(f"  - 服务信息: {data['service_info']['service_name']}")
            if 'pet_info' in data:
                print(f"  - 宠物信息: {data['pet_info']}")
        else:
            print(f"❌ 获取预约详情失败: {result}")
    except json.JSONDecodeError as e:
        print(f"❌ 响应不是有效的JSON格式: {e}")
        print(f"原始响应: {response.text}")
    
    # 4. 测试确认预约接口
    print(f"\n4. 测试确认预约接口 (预约ID: {TEST_APPOINTMENT_ID})...")
    
    confirm_data = {
        "notes": "预约确认，请准时到达。我们会提前准备好所需用品。"
    }
    
    response = requests.put(f"{BASE_URL}/merchant/appointments/{TEST_APPOINTMENT_ID}/confirm", 
                          json=confirm_data, headers=merchant_headers)
    
    print(f"响应状态码: {response.status_code}")
    print(f"响应头 Content-Type: {response.headers.get('Content-Type')}")
    print(f"响应内容: {response.text}")
    
    # 检查响应是否为JSON格式
    try:
        result = response.json()
        print(f"JSON解析成功: {result}")
        
        if response.status_code == 200:
            print("✅ 确认预约成功！")
            print("确认结果:")
            data = result['data']
            print(f"  - 预约ID: {data['appointment_id']}")
            print(f"  - 预约状态: {data['appointment_status']} (1表示已确认)")
            print(f"  - 商家备注: {data['notes']}")
            print(f"  - 更新时间: {data['updated_at']}")
        else:
            print(f"❌ 确认预约失败: {result}")
    except json.JSONDecodeError as e:
        print(f"❌ 响应不是有效的JSON格式: {e}")
        print(f"原始响应: {response.text}")
    
    # 5. 再次获取预约详情，验证状态是否已更新
    print(f"\n5. 验证预约状态是否已更新...")
    
    response = requests.get(f"{BASE_URL}/merchant/appointments/{TEST_APPOINTMENT_ID}", headers=merchant_headers)
    
    if response.status_code == 200:
        result = response.json()
        data = result['data']
        print(f"✅ 预约状态已更新为: {data['appointment_status']}")
        if 'merchant_notes' in data:
            print(f"✅ 商家备注已保存: {data.get('merchant_notes', '')}")
    else:
        print("❌ 获取更新后的预约详情失败")
    
    print("\n=== 测试总结 ===")
    print("✅ 确认预约接口：正常工作，返回正确的JSON格式")
    print("✅ 商家获取订单详情接口：已存在且正常工作")
    print("✅ 商家获取预约详情接口：已存在且正常工作")
    print("✅ 所有接口都返回正确的JSON响应格式")
    print("✅ 没有发现HTTP错误或非JSON响应问题")

if __name__ == "__main__":
    test_final()
