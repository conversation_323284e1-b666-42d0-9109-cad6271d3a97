#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取服务接口失败测试用例
测试各种异常情况和边界条件
"""

import requests
import json
import time
from datetime import datetime

# 测试配置
BASE_URL = "http://localhost:5000/api"
SERVICES_URL = f"{BASE_URL}/services"

class ServiceFailureTests:
    def __init__(self):
        self.test_results = []
        self.passed = 0
        self.failed = 0
    
    def log_test(self, test_name, expected_result, actual_result, passed):
        """记录测试结果"""
        result = {
            'test_name': test_name,
            'expected': expected_result,
            'actual': actual_result,
            'passed': passed,
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        if passed:
            self.passed += 1
            print(f"✅ {test_name}")
        else:
            self.failed += 1
            print(f"❌ {test_name}")
            print(f"   Expected: {expected_result}")
            print(f"   Actual: {actual_result}")
    
    def test_invalid_service_id_formats(self):
        """测试无效的服务ID格式"""
        invalid_ids = [
            "",  # 空字符串
            " ",  # 空格
            "123",  # 纯数字
            "abc",  # 纯字母
            "S",  # 只有前缀
            "S123456789012345",  # 过长ID
            "X1234567890",  # 错误前缀
            "s1234567890",  # 小写前缀
            "S-123456789",  # 包含特殊字符
            "S 123456789",  # 包含空格
            "S123456789\n",  # 包含换行符
            "S123456789\t",  # 包含制表符
            "null",  # null字符串
            "undefined",  # undefined字符串
            "../S1234567890",  # 路径遍历
            "S1234567890;DROP TABLE service;",  # SQL注入尝试
        ]
        
        for invalid_id in invalid_ids:
            try:
                response = requests.get(f"{SERVICES_URL}/{invalid_id}")
                expected_code = 30001
                actual_code = response.json().get('code')
                
                passed = actual_code == expected_code
                self.log_test(
                    f"Invalid Service ID: '{invalid_id}'",
                    f"Error code {expected_code}",
                    f"Error code {actual_code}",
                    passed
                )
            except Exception as e:
                self.log_test(
                    f"Invalid Service ID: '{invalid_id}'",
                    "Error response",
                    f"Exception: {str(e)}",
                    False
                )
    
    def test_invalid_query_parameters(self):
        """测试无效的查询参数"""
        invalid_params = [
            {"page": -1},  # 负数页码
            {"page": 0},   # 零页码
            {"page": "abc"},  # 非数字页码
            {"page": ""},  # 空页码
            {"page": " "},  # 空格页码
            {"page": "1.5"},  # 小数页码
            {"page": "999999999999999999999"},  # 超大页码
            {"limit": -1},  # 负数限制
            {"limit": 0},   # 零限制
            {"limit": "abc"},  # 非数字限制
            {"limit": ""},  # 空限制
            {"limit": "1001"},  # 超大限制
            {"service_kind": -1},  # 负数服务类型
            {"service_kind": 5},   # 超出范围的服务类型
            {"service_kind": "abc"},  # 非数字服务类型
            {"service_kind": ""},  # 空服务类型
            {"service_kind": " "},  # 空格服务类型
            {"keyword": ""},  # 空关键词（这个应该是正常的）
            {"keyword": " " * 1000},  # 超长关键词
            {"unknown_param": "value"},  # 未知参数
        ]
        
        for params in invalid_params:
            try:
                response = requests.get(SERVICES_URL, params=params)
                
                # 大多数无效参数应该返回400错误或者被忽略但仍返回200
                if response.status_code in [200, 400]:
                    passed = True
                    result = f"Status {response.status_code}"
                else:
                    passed = False
                    result = f"Unexpected status {response.status_code}"
                
                self.log_test(
                    f"Invalid params: {params}",
                    "Status 200 or 400",
                    result,
                    passed
                )
            except Exception as e:
                self.log_test(
                    f"Invalid params: {params}",
                    "Valid response",
                    f"Exception: {str(e)}",
                    False
                )
    
    def test_malformed_requests(self):
        """测试格式错误的请求"""
        test_cases = [
            {
                "name": "Invalid HTTP method POST",
                "method": "POST",
                "url": SERVICES_URL,
                "expected_status": 405
            },
            {
                "name": "Invalid HTTP method PUT",
                "method": "PUT", 
                "url": SERVICES_URL,
                "expected_status": 405
            },
            {
                "name": "Invalid HTTP method DELETE",
                "method": "DELETE",
                "url": SERVICES_URL,
                "expected_status": 405
            },
            {
                "name": "Invalid Content-Type",
                "method": "GET",
                "url": SERVICES_URL,
                "headers": {"Content-Type": "text/plain"},
                "expected_status": 200  # GET请求通常忽略Content-Type
            }
        ]
        
        for case in test_cases:
            try:
                method = case["method"]
                url = case["url"]
                headers = case.get("headers", {})
                expected_status = case["expected_status"]
                
                response = requests.request(method, url, headers=headers)
                actual_status = response.status_code
                
                passed = actual_status == expected_status
                self.log_test(
                    case["name"],
                    f"Status {expected_status}",
                    f"Status {actual_status}",
                    passed
                )
            except Exception as e:
                self.log_test(
                    case["name"],
                    f"Status {case['expected_status']}",
                    f"Exception: {str(e)}",
                    False
                )
    
    def test_server_stress(self):
        """测试服务器压力情况"""
        # 快速连续请求
        print("\n🔥 Testing rapid requests...")
        for i in range(10):
            try:
                start_time = time.time()
                response = requests.get(SERVICES_URL, timeout=1)
                end_time = time.time()
                
                response_time = end_time - start_time
                passed = response.status_code == 200 and response_time < 1.0
                
                self.log_test(
                    f"Rapid request #{i+1}",
                    "Status 200, response < 1s",
                    f"Status {response.status_code}, response {response_time:.3f}s",
                    passed
                )
            except Exception as e:
                self.log_test(
                    f"Rapid request #{i+1}",
                    "Successful response",
                    f"Exception: {str(e)}",
                    False
                )
            time.sleep(0.1)  # 短暂间隔
    
    def test_edge_cases(self):
        """测试边界情况"""
        edge_cases = [
            {"page": 1, "limit": 1},  # 最小分页
            {"page": 1000, "limit": 1},  # 大页码
            {"keyword": "不存在的服务名称"},  # 不存在的关键词
            {"service_kind": 0, "keyword": "不匹配"},  # 类型匹配但关键词不匹配
            {"service_kind": 1, "keyword": "美容"},  # 关键词匹配但类型不匹配
        ]
        
        for params in edge_cases:
            try:
                response = requests.get(SERVICES_URL, params=params)
                data = response.json()
                
                # 边界情况应该返回200但可能是空结果
                passed = (response.status_code == 200 and 
                         'data' in data and 
                         'services' in data['data'])
                
                services_count = len(data['data']['services']) if passed else 0
                
                self.log_test(
                    f"Edge case: {params}",
                    "Status 200 with valid structure",
                    f"Status {response.status_code}, {services_count} services",
                    passed
                )
            except Exception as e:
                self.log_test(
                    f"Edge case: {params}",
                    "Valid response",
                    f"Exception: {str(e)}",
                    False
                )
    
    def run_all_tests(self):
        """运行所有失败测试"""
        print("🧪 开始运行获取服务接口失败测试...")
        print("=" * 60)
        
        print("\n📋 测试无效服务ID格式...")
        self.test_invalid_service_id_formats()
        
        print("\n📋 测试无效查询参数...")
        self.test_invalid_query_parameters()
        
        print("\n📋 测试格式错误的请求...")
        self.test_malformed_requests()
        
        print("\n📋 测试边界情况...")
        self.test_edge_cases()
        
        print("\n📋 测试服务器压力...")
        self.test_server_stress()
        
        # 输出测试总结
        print("\n" + "=" * 60)
        print(f"📊 测试总结:")
        print(f"   总测试数: {len(self.test_results)}")
        print(f"   通过: {self.passed}")
        print(f"   失败: {self.failed}")
        print(f"   成功率: {(self.passed/len(self.test_results)*100):.1f}%")
        
        return self.test_results

if __name__ == "__main__":
    # 检查服务器是否运行
    try:
        response = requests.get(f"{BASE_URL}/services", timeout=5)
        print("✅ 服务器连接正常，开始测试...")
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("请确保Flask应用正在运行在 http://localhost:5000")
        exit(1)
    
    # 运行测试
    tester = ServiceFailureTests()
    results = tester.run_all_tests()
    
    # 保存测试结果到文件
    with open('service_failure_test_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 详细测试结果已保存到: service_failure_test_results.json")
