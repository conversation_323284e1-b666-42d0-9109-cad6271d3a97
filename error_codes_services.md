# 获取服务接口错误码文档

## 服务相关错误码 (30000-30099)

### 基础错误码
| 错误码 | 错误信息 | 说明 | 触发条件 |
|--------|----------|------|----------|
| 30001 | 服务不存在 | 请求的服务ID在数据库中不存在 | 查询不存在的service_id |
| 30012 | 服务数据不可用 | 服务数据库连接异常或数据结构损坏 | 系统内部错误 |
| 30099 | 服务暂时不可用，请稍后重试 | 系统发生未知错误 | 捕获所有未预期异常 |

### 参数验证错误码 (30002-30011)
| 错误码 | 错误信息 | 说明 | 示例 |
|--------|----------|------|------|
| 30002 | 页码必须大于0 | page参数小于1 | `?page=0` 或 `?page=-1` |
| 30003 | 页码超出允许范围 | page参数超过10000 | `?page=10001` |
| 30004 | 页码格式错误，必须为正整数 | page参数不是有效整数 | `?page=abc` 或 `?page=1.5` |
| 30005 | 每页数量必须大于0 | limit参数小于1 | `?limit=0` 或 `?limit=-5` |
| 30006 | 每页数量不能超过100 | limit参数超过100 | `?limit=101` |
| 30007 | 每页数量格式错误，必须为正整数 | limit参数不是有效整数 | `?limit=abc` |
| 30008 | 服务类型无效，必须为0-3之间的整数 | service_kind不在有效范围内 | `?service_kind=5` |
| 30009 | 服务类型格式错误，必须为整数 | service_kind不是整数 | `?service_kind=abc` |
| 30010 | 搜索关键词长度不能超过50个字符 | keyword参数过长 | `?keyword=很长很长的关键词...` |
| 30011 | 搜索关键词包含非法字符 | keyword包含特殊字符 | `?keyword=<script>` |

### 分页相关错误码 (30013)
| 错误码 | 错误信息 | 说明 | 示例 |
|--------|----------|------|------|
| 30013 | 页码超出范围，最大页码为{max_page} | 请求的页码超过实际数据页数 | 总共3页数据，请求第5页 |

### 服务ID验证错误码 (30014-30020)
| 错误码 | 错误信息 | 说明 | 示例 |
|--------|----------|------|------|
| 30014 | 服务ID不能为空 | service_id参数为空 | `/api/services/` |
| 30015 | 服务ID长度无效 | service_id长度超过限制 | 超长的service_id |
| 30016 | 服务ID格式无效，应为S+10位数字 | service_id格式不符合规范 | `abc123` 或 `S123` |
| 30017 | 服务数据格式错误 | 数据库中的服务数据不是有效对象 | 数据损坏 |
| 30018 | 服务数据不完整，缺少字段: {fields} | 服务数据缺少必要字段 | 缺少service_name等 |
| 30019 | 服务价格数据异常 | 服务价格为负数 | price < 0 |
| 30020 | 服务类型数据异常 | 服务类型不在有效范围 | kind不在0-3范围 |

## 服务类型说明
- 0: 美容服务
- 1: 寄养服务  
- 2: 清洁服务
- 3: 训练服务

## API接口说明

### 获取服务列表
```
GET /api/services
```

**查询参数:**
- `page` (可选): 页码，默认1，范围1-10000
- `limit` (可选): 每页数量，默认10，范围1-100  
- `service_kind` (可选): 服务类型，0-3
- `keyword` (可选): 搜索关键词，最长50字符

**成功响应:**
```json
{
  "code": 200,
  "data": {
    "total": 100,
    "page": 1,
    "limit": 10,
    "max_page": 10,
    "services": [...]
  },
  "message": "获取成功"
}
```

**错误响应示例:**
```json
{
  "code": 30004,
  "data": null,
  "message": "页码格式错误，必须为正整数"
}
```

### 获取服务详情
```
GET /api/services/{service_id}
```

**路径参数:**
- `service_id`: 服务ID，格式为S+10位数字

**成功响应:**
```json
{
  "code": 200,
  "data": {
    "service_id": "S1234567890",
    "service_name": "宠物美容",
    "service_kind": 0,
    "service_price": 88.0,
    "service_time": 120,
    "service_description": "...",
    "service_picture": "...",
    "merchant_id": "M1234567"
  },
  "message": "获取成功"
}
```

**错误响应示例:**
```json
{
  "code": 30001,
  "data": null,
  "message": "服务不存在"
}
```

## 测试用例

### 失败测试用例

#### 1. 参数验证失败
```bash
# 无效页码
GET /api/services?page=0          # 返回30002
GET /api/services?page=-1         # 返回30002  
GET /api/services?page=abc        # 返回30004
GET /api/services?page=10001      # 返回30003

# 无效限制
GET /api/services?limit=0         # 返回30005
GET /api/services?limit=101       # 返回30006
GET /api/services?limit=abc       # 返回30007

# 无效服务类型
GET /api/services?service_kind=5  # 返回30008
GET /api/services?service_kind=abc # 返回30009

# 无效关键词
GET /api/services?keyword=<script> # 返回30011
```

#### 2. 服务ID验证失败
```bash
# 无效服务ID格式
GET /api/services/              # 返回30014
GET /api/services/abc           # 返回30016
GET /api/services/S123          # 返回30016
GET /api/services/123456789012345 # 返回30015

# 不存在的服务
GET /api/services/S9999999999   # 返回30001
```

#### 3. 分页超出范围
```bash
# 假设只有3页数据
GET /api/services?page=5        # 返回30013
```

### 边界测试用例
```bash
# 边界值测试
GET /api/services?page=1&limit=1     # 最小分页
GET /api/services?page=10000&limit=100 # 最大分页
GET /api/services?service_kind=0     # 最小服务类型
GET /api/services?service_kind=3     # 最大服务类型
GET /api/services?keyword=12345678901234567890123456789012345678901234567890 # 50字符关键词
```

## 错误处理最佳实践

1. **参数验证优先**: 先验证所有输入参数的格式和范围
2. **业务逻辑验证**: 再验证业务规则（如页码是否超出实际范围）
3. **数据完整性检查**: 验证返回数据的完整性和有效性
4. **异常捕获**: 使用try-catch捕获所有未预期的错误
5. **错误日志**: 记录详细的错误信息用于调试
6. **用户友好**: 返回清晰明确的错误信息给前端
