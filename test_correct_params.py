#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证正确请求参数的测试脚本
"""

import requests
import time

BASE_URL = "http://localhost:5000/api"

def test_correct_params():
    print("🧪 测试正确的请求参数格式...")
    print("=" * 50)
    
    # 测试1: 发送验证码 - 使用正确的字段名
    print("📱 测试1: 发送验证码（使用user_phone字段）")
    try:
        response = requests.post(f"{BASE_URL}/auth/send-code", 
                               json={"user_phone": "***********"})
        print(f"✅ 状态码: {response.status_code}")
        data = response.json()
        print(f"✅ 响应格式: code={data['code']}, message='{data['message']}'")
        print(f"✅ 数据: {data['data']}")
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    print()
    
    # 测试2: 用户注册 - 使用正确的字段名
    print("👤 测试2: 用户注册（使用user_phone和user_verification_code字段）")
    try:
        # 先发送验证码
        requests.post(f"{BASE_URL}/auth/send-code", json={"user_phone": "13900139001"})
        time.sleep(1)
        
        # 注册（注意：这里会失败因为验证码不匹配，但可以验证请求格式）
        response = requests.post(f"{BASE_URL}/auth/register", 
                               json={
                                   "user_phone": "13900139001", 
                                   "user_verification_code": "123456"
                               })
        print(f"✅ 状态码: {response.status_code}")
        data = response.json()
        print(f"✅ 响应格式: code={data['code']}, message='{data['message']}'")
        if response.status_code == 200:
            print(f"✅ 用户信息字段: {list(data['data']['user_info'].keys())}")
        else:
            print(f"ℹ️  预期的验证码错误（因为验证码是随机生成的）")
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    print()
    
    # 测试3: 添加宠物 - 需要先登录获取token
    print("🐕 测试3: 宠物添加请求参数格式")
    print("请求参数应该包含:")
    print("- pet_kind: 宠物种类")
    print("- pet_age: 宠物出生日期")
    print("- pet_weight: 宠物体重")
    print("（注意：需要先登录获取token才能测试）")
    
    print()
    
    # 测试4: 商家登录 - 使用正确的字段名
    print("🏪 测试4: 商家登录（使用merchant_account和merchant_password字段）")
    try:
        response = requests.post(f"{BASE_URL}/merchant/auth/login", 
                               json={
                                   "merchant_account": "petshop001", 
                                   "merchant_password": "123456"
                               })
        print(f"✅ 状态码: {response.status_code}")
        data = response.json()
        print(f"✅ 响应格式: code={data['code']}, message='{data['message']}'")
        if response.status_code == 200:
            print(f"✅ 商家信息字段: {list(data['data']['merchant_info'].keys())}")
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    print()
    print("=" * 50)
    print("🎯 总结:")
    print("1. 所有请求参数现在都使用数据库表字段名")
    print("2. 响应格式为: {code, data, message}")
    print("3. 不再包含timestamp字段")
    print("4. 字段名完全匹配您提供的数据库表设计")
    print()
    print("📋 APIFOX测试要点:")
    print("- 发送验证码: POST /auth/send-code {\"user_phone\": \"***********\"}")
    print("- 用户注册: POST /auth/register {\"user_phone\": \"...\", \"user_verification_code\": \"...\"}")
    print("- 用户登录: POST /auth/login {\"user_phone\": \"...\", \"user_verification_code\": \"...\"}")
    print("- 添加宠物: POST /pets {\"pet_kind\": \"狗\", \"pet_age\": \"2022-01-01\", \"pet_weight\": \"5.5\"}")
    print("- 商家登录: POST /merchant/auth/login {\"merchant_account\": \"...\", \"merchant_password\": \"...\"}")

if __name__ == "__main__":
    test_correct_params()
