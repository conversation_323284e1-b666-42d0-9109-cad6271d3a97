# 宠物服务平台 API

这是一个完整的宠物服务平台后端API，使用Flask框架开发，支持用户端和商家端的所有功能。

## 🚀 快速开始

### 环境要求
- Python 3.7+
- pip

### 安装和运行

#### 方法1: 使用启动脚本（Windows）
```bash
start.bat
```

#### 方法2: 手动启动
```bash
# 安装依赖
pip install -r requirements.txt

# 启动应用
python app.py
```

应用将在 `http://localhost:5000` 启动，API基础地址为 `http://localhost:5000/api`

## 📋 功能特性

### 用户端功能
- ✅ 用户注册/登录（手机号+验证码）
- ✅ 用户信息管理
- ✅ 宠物信息管理（增删查）
- ✅ 服务浏览和搜索
- ✅ 订单创建和管理
- ✅ 在线支付（模拟）
- ✅ 服务预约管理
- ✅ 服务评价系统

### 商家端功能
- ✅ 商家注册/登录（账号+密码）
- ✅ 商家信息管理
- ✅ 服务管理（增删改查）
- ✅ 订单处理和管理
- ✅ 预约确认和管理
- ✅ 数据统计和分析

### 通用功能
- ✅ 文件上传（图片）
- ✅ 可用时间段查询
- ✅ JWT Token认证
- ✅ 统一响应格式
- ✅ 完善的错误处理

## 🔧 技术栈

- **后端框架**: Flask 2.3.3
- **认证**: JWT (PyJWT)
- **跨域**: Flask-CORS
- **数据存储**: 内存存储（演示用，生产环境建议使用数据库）

## 📖 API文档

### 接口总览

#### 用户端API
| 模块 | 接口数量 | 主要功能 |
|------|----------|----------|
| 用户认证 | 4个 | 注册、登录、验证码、信息更新 |
| 宠物管理 | 3个 | 添加、查询、删除宠物 |
| 服务模块 | 2个 | 服务列表、服务详情 |
| 订单模块 | 5个 | 创建、支付、查询、详情、取消 |
| 预约模块 | 3个 | 创建、修改、取消预约 |
| 评价模块 | 2个 | 提交评价、查询评价 |

#### 商家端API
| 模块 | 接口数量 | 主要功能 |
|------|----------|----------|
| 商家认证 | 3个 | 注册、登录、信息更新 |
| 服务管理 | 4个 | 添加、查询、编辑、删除服务 |
| 订单管理 | 4个 | 查询、确认、完成、取消订单 |
| 预约管理 | 4个 | 查询、确认、修改、取消预约 |
| 数据统计 | 1个 | 营收和服务统计 |

#### 通用接口
| 功能 | 接口数量 | 说明 |
|------|----------|------|
| 文件上传 | 1个 | 支持图片上传 |
| 时间查询 | 1个 | 获取可用时间段 |

### 详细文档
- [完整API设计文档](API_Design.md)
- [API接口总览](API_Summary.md)
- [数据库设计建议](Database_Design_Suggestions.md)
- [项目实施指南](Implementation_Guide.md)

## 🧪 测试指南

### APIFOX测试
详细的APIFOX测试指南请查看：[APIFOX测试指南](APIFOX_Test_Guide.md)

### 预置测试数据

**测试商家账号**:
- 账号: `petshop001`
- 密码: `123456`

**测试服务**:
- 服务名: `宠物基础美容套餐`
- 价格: `88.00元`
- 类型: `美容`

### 测试流程
1. 启动应用
2. 在APIFOX中配置基础URL: `http://localhost:5000/api`
3. 按照测试指南进行接口测试

## 📊 数据模型

### 核心实体
- **用户 (Users)**: 用户基本信息
- **宠物 (Pets)**: 宠物信息
- **商家 (Merchants)**: 商家信息
- **服务 (Services)**: 服务项目
- **订单 (Orders)**: 订单信息
- **预约 (Appointments)**: 预约信息
- **评价 (Evaluations)**: 服务评价

### 状态说明

**订单状态**:
- 0: 已取消
- 1: 已支付
- 2: 已预约
- 3: 已完成
- 4: 未支付

**预约状态**:
- 0: 待确认
- 1: 已确认
- 2: 已取消
- 3: 已完成

## 🔐 安全特性

- JWT Token认证
- 密码哈希存储
- 请求参数验证
- 权限控制
- 文件类型和大小限制

## 🚧 开发说明

### 当前实现
- 使用内存存储数据（适合演示和测试）
- 模拟短信验证码（控制台输出）
- 模拟支付接口
- 模拟文件上传

### 生产环境建议
- 使用MySQL/PostgreSQL数据库
- 集成真实短信服务
- 集成支付宝/微信支付
- 使用云存储服务（OSS/COS）
- 添加Redis缓存
- 添加日志系统
- 添加监控和告警

## 📝 更新日志

### v1.0.0 (2024-01-01)
- ✅ 完成所有核心API接口
- ✅ 实现用户端和商家端功能
- ✅ 添加JWT认证
- ✅ 完善错误处理
- ✅ 提供完整测试指南

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

MIT License

## 📞 联系方式

如有问题，请通过以下方式联系：
- 创建Issue
- 发送邮件

---

**注意**: 这是一个演示项目，包含完整的API功能实现。在生产环境中使用前，请根据实际需求进行相应的安全加固和性能优化。
