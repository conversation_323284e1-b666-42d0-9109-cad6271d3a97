# 宠物服务平台 API 接口总览

## 用户端 API 接口列表

### 用户认证模块
| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/auth/send-code` | 发送验证码 |
| POST | `/auth/register` | 用户注册 |
| POST | `/auth/login` | 用户登录 |
| PUT | `/user/profile` | 更新用户信息 |

### 宠物管理模块
| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/pets` | 添加宠物 |
| GET | `/pets` | 获取宠物列表 |
| DELETE | `/pets/{pet_id}` | 删除宠物 |

### 服务模块
| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/services` | 获取服务列表 |
| GET | `/services/{service_id}` | 获取服务详情 |

### 订单模块
| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/orders` | 创建订单 |
| POST | `/orders/{order_id}/payment-callback` | 订单支付回调 |
| GET | `/orders` | 获取订单列表 |
| GET | `/orders/{order_id}` | 获取订单详情 |
| PUT | `/orders/{order_id}/cancel` | 取消订单 |

### 预约模块
| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/appointments` | 创建预约 |
| PUT | `/appointments/{appointment_id}` | 修改预约 |
| PUT | `/appointments/{appointment_id}/cancel` | 取消预约 |

### 评价模块
| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/evaluations` | 提交评价 |
| GET | `/services/{service_id}/evaluations` | 获取服务评价列表 |

## 商家端 API 接口列表

### 商家认证模块
| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/merchant/auth/register` | 商家注册 |
| POST | `/merchant/auth/login` | 商家登录 |
| PUT | `/merchant/profile` | 更新商家信息 |

### 服务管理模块
| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/merchant/services` | 添加服务 |
| GET | `/merchant/services` | 获取商家服务列表 |
| PUT | `/merchant/services/{service_id}` | 编辑服务 |
| DELETE | `/merchant/services/{service_id}` | 删除服务 |

### 订单管理模块
| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/merchant/orders` | 获取订单列表 |
| PUT | `/merchant/orders/{order_id}/confirm` | 确认订单 |
| PUT | `/merchant/orders/{order_id}/complete` | 完成订单 |
| PUT | `/merchant/orders/{order_id}/cancel` | 取消订单 |

### 预约管理模块
| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/merchant/appointments` | 获取预约列表 |
| PUT | `/merchant/appointments/{appointment_id}/confirm` | 确认预约 |
| PUT | `/merchant/appointments/{appointment_id}` | 修改预约 |
| PUT | `/merchant/appointments/{appointment_id}/cancel` | 取消预约 |

### 数据统计模块
| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/merchant/statistics` | 获取商家统计数据 |

## 通用工具接口

| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/upload` | 文件上传 |
| GET | `/available-times` | 获取可用时间段 |

## 数据库表对应关系

### 用户相关
- `users` 表 → 用户认证、用户信息管理
- `pets` 表 → 宠物管理

### 服务相关
- `service` 表 → 服务展示、服务管理

### 交易相关
- `order` 表 → 订单管理
- `payment` 表 → 支付管理
- `appointment` 表 → 预约管理

### 评价相关
- `evaluation` 表 → 评价管理

### 商家相关
- `merchant` 表 → 商家认证、商家信息管理

## 主要业务流程

### 用户端业务流程
1. **注册/登录** → 发送验证码 → 验证码登录/注册
2. **宠物管理** → 添加宠物信息 → 管理宠物列表
3. **服务购买** → 浏览服务 → 创建订单 → 支付 → 预约 → 服务完成 → 评价
4. **订单管理** → 查看订单 → 修改预约 → 取消订单

### 商家端业务流程
1. **注册/登录** → 账号密码登录/注册
2. **服务管理** → 添加服务 → 编辑服务 → 管理服务列表
3. **订单处理** → 查看订单 → 确认订单 → 完成服务
4. **预约管理** → 查看预约 → 确认预约 → 修改预约时间
5. **数据统计** → 查看营收数据 → 服务统计

## 技术要点

### 认证机制
- 用户端：手机号 + 验证码
- 商家端：账号 + 密码
- 统一使用 JWT Token 进行身份验证

### 支付集成
- 集成支付宝支付
- 支持订单退款
- 商家取消订单提供120%补偿

### 文件管理
- 支持图片上传（服务图片、评价图片）
- 文件大小限制：5MB
- 支持格式：jpg, png, gif

### 数据验证
- 宠物信息验证（年龄、体重、种类）
- 服务时间可用性验证
- 订单状态流转控制

### 通知机制
- 短信验证码发送
- 订单状态变更通知
- 预约确认/取消通知

## 扩展建议

1. **实时通信**：可考虑添加 WebSocket 接口用于实时消息推送
2. **地理位置**：可添加基于地理位置的服务推荐
3. **优惠券系统**：可扩展优惠券和促销活动管理
4. **客服系统**：可添加在线客服聊天功能
5. **数据分析**：可扩展更详细的数据分析和报表功能
