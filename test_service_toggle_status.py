#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试服务上架/下架功能
"""

import requests
import json

BASE_URL = "http://localhost:5000/api"

def get_merchant_token():
    """获取商家登录Token"""
    login_data = {
        "merchant_account": "petshop001",
        "merchant_password": "123456"
    }
    
    response = requests.post(f"{BASE_URL}/merchant/auth/login", json=login_data)
    if response.status_code == 200:
        data = response.json()
        return data['data']['token']
    else:
        print(f"商家登录失败: {response.text}")
        return None

def get_merchant_services(token):
    """获取商家服务列表"""
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/merchant/services", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        return data['data']['services']
    else:
        print(f"获取服务列表失败: {response.text}")
        return []

def test_toggle_service_status(token, service_id, target_status, description):
    """测试切换服务状态"""
    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
    
    data = {"is_active": target_status}
    
    try:
        response = requests.put(f"{BASE_URL}/merchant/services/{service_id}/toggle-status", 
                               headers=headers, json=data)
        
        result = response.json()
        
        if response.status_code == 200 and result.get('code') == 200:
            print(f"✅ {description}")
            print(f"   Service ID: {service_id}")
            print(f"   Target Status: {target_status}")
            print(f"   Result: {result.get('message')}")
            print(f"   Action: {result['data'].get('action')}")
        else:
            print(f"❌ {description}")
            print(f"   Service ID: {service_id}")
            print(f"   HTTP Status: {response.status_code}")
            print(f"   Error: {result.get('message')}")
        print()
        
        return result
        
    except Exception as e:
        print(f"❌ {description}")
        print(f"   Error: {e}")
        print()
        return None

def test_get_services_by_status(token, is_active, description):
    """测试按状态获取服务"""
    headers = {"Authorization": f"Bearer {token}"}
    
    params = {"is_active": str(is_active).lower()} if is_active is not None else {}
    
    try:
        response = requests.get(f"{BASE_URL}/merchant/services", headers=headers, params=params)
        
        if response.status_code == 200:
            data = response.json()
            services = data['data']['services']
            print(f"✅ {description}")
            print(f"   Found {len(services)} services")
            for service in services:
                status = "上架" if service.get('is_active', True) else "下架"
                print(f"   - {service['service_name']} ({status})")
        else:
            print(f"❌ {description}")
            print(f"   HTTP Status: {response.status_code}")
        print()
        
    except Exception as e:
        print(f"❌ {description}")
        print(f"   Error: {e}")
        print()

def test_user_get_services(description):
    """测试用户端获取服务（应该只看到上架的）"""
    try:
        response = requests.get(f"{BASE_URL}/services")
        
        if response.status_code == 200:
            data = response.json()
            services = data['data']['services']
            print(f"✅ {description}")
            print(f"   Found {len(services)} services (only active should be visible)")
            for service in services:
                status = "上架" if service.get('is_active', True) else "下架"
                print(f"   - {service['service_name']} ({status})")
        else:
            print(f"❌ {description}")
            print(f"   HTTP Status: {response.status_code}")
        print()
        
    except Exception as e:
        print(f"❌ {description}")
        print(f"   Error: {e}")
        print()

def test_error_scenarios(token):
    """测试错误场景"""
    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
    
    # 测试无效的服务ID
    print("📋 测试错误场景:")
    
    # 1. 无效服务ID格式
    response = requests.put(f"{BASE_URL}/merchant/services/INVALID_ID/toggle-status", 
                           headers=headers, json={"is_active": False})
    result = response.json()
    if result.get('code') == 30016:
        print("✅ 无效服务ID格式 - 正确返回错误码30016")
    else:
        print(f"❌ 无效服务ID格式 - 期望30016，实际{result.get('code')}")
    
    # 2. 不存在的服务ID
    response = requests.put(f"{BASE_URL}/merchant/services/S9999999999/toggle-status", 
                           headers=headers, json={"is_active": False})
    result = response.json()
    if result.get('code') == 30001:
        print("✅ 不存在的服务ID - 正确返回错误码30001")
    else:
        print(f"❌ 不存在的服务ID - 期望30001，实际{result.get('code')}")
    
    # 3. 无效的状态参数
    services = get_merchant_services(token)
    if services:
        service_id = services[0]['service_id']
        response = requests.put(f"{BASE_URL}/merchant/services/{service_id}/toggle-status", 
                               headers=headers, json={"is_active": "invalid"})
        result = response.json()
        if result.get('code') == 32001:
            print("✅ 无效状态参数 - 正确返回错误码32001")
        else:
            print(f"❌ 无效状态参数 - 期望32001，实际{result.get('code')}")
    
    print()

def main():
    print("🧪 测试服务上架/下架功能...")
    print("=" * 60)
    
    # 获取商家Token
    print("🔑 获取商家登录Token...")
    token = get_merchant_token()
    if not token:
        print("❌ 无法获取商家Token，测试终止")
        return
    print(f"✅ 成功获取Token")
    print()
    
    # 获取商家服务列表
    print("📋 获取商家服务列表...")
    services = get_merchant_services(token)
    if not services:
        print("❌ 没有找到服务，测试终止")
        return
    
    print(f"✅ 找到 {len(services)} 个服务:")
    for service in services:
        status = "上架" if service.get('is_active', True) else "下架"
        print(f"   - {service['service_name']} ({service['service_id']}) - {status}")
    print()
    
    # 测试下架功能
    active_service = None
    inactive_service = None
    
    for service in services:
        if service.get('is_active', True):
            active_service = service
        else:
            inactive_service = service
    
    if active_service:
        print("📋 测试下架功能:")
        test_toggle_service_status(token, active_service['service_id'], False, 
                                  f"下架服务: {active_service['service_name']}")
    
    if inactive_service:
        print("📋 测试上架功能:")
        test_toggle_service_status(token, inactive_service['service_id'], True, 
                                  f"上架服务: {inactive_service['service_name']}")
    
    # 测试状态切换（不指定目标状态）
    if services:
        print("📋 测试状态切换:")
        headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
        service_id = services[0]['service_id']
        response = requests.put(f"{BASE_URL}/merchant/services/{service_id}/toggle-status", 
                               headers=headers, json={})
        result = response.json()
        if response.status_code == 200:
            print(f"✅ 状态切换成功: {result['data'].get('action')}")
        else:
            print(f"❌ 状态切换失败: {result.get('message')}")
        print()
    
    # 测试按状态过滤
    print("📋 测试按状态过滤:")
    test_get_services_by_status(token, True, "获取上架服务")
    test_get_services_by_status(token, False, "获取下架服务")
    test_get_services_by_status(token, None, "获取所有服务")
    
    # 测试用户端只能看到上架服务
    print("📋 测试用户端服务可见性:")
    test_user_get_services("用户端获取服务（应该只显示上架的）")
    
    # 测试错误场景
    test_error_scenarios(token)
    
    print("🎉 测试完成！")

if __name__ == "__main__":
    main()
